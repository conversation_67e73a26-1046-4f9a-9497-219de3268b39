[metadata]
name = nvidia-cutlass
version = 3.4.0.0

[options]
packages =
  cutlass
  cutlass.backend
  cutlass.backend.evt
  cutlass.backend.evt.backend
  cutlass.backend.evt.frontend
  cutlass.backend.evt.ir
  cutlass.backend.evt.passes
  cutlass.backend.utils
  cutlass.emit
  cutlass.epilogue
  cutlass.op
  cutlass.utils
  cutlass_library
  cutlass_library.source
  pycute
package_dir =
  cutlass=python/cutlass
  cutlass_library=python/cutlass_library
  cutlass_library.source=.
  pycute=python/pycute
include_package_data = True

[options.package_data]
cutlass_library.source = include/**/*, examples/**/*, tools/**/*

[options.exclude_package_data]
cutlass_library.source = include/**/*.py, examples/**/*.py, tools/**/*.py
