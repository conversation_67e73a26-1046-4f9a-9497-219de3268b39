<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass Directory Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">cutlass Directory Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Directory dependency graph for cutlass:</div>
<div class="dyncontent">
<div class="center"><img src="dir_6baf2bb612a2f0daa69af3101ede80a1_dep.png" border="0" usemap="#dir__6baf2bb612a2f0daa69af3101ede80a1__dep" alt="cutlass"/></div>
<map name="dir__6baf2bb612a2f0daa69af3101ede80a1__dep" id="dir__6baf2bb612a2f0daa69af3101ede80a1__dep">
</map>
</div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="subdirs"></a>
Directories</h2></td></tr>
<tr class="memitem:dir_048c1df36ab9c2efbb0733edba6291c9"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_048c1df36ab9c2efbb0733edba6291c9.html">arch</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:dir_d9e7e9e63637345b8b26a82972709306"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:dir_9aa36bd9cfad59a1f88859a38871c977"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_9aa36bd9cfad59a1f88859a38871c977.html">gemm</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:dir_2296cf082f2778f9a3503c8ea1010763"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_2296cf082f2778f9a3503c8ea1010763.html">layout</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:dir_df998829b150afe92f54393d2430470d"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_df998829b150afe92f54393d2430470d.html">platform</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:dir_ac488927e63b76ba9cb3ad9c317bbde9"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_ac488927e63b76ba9cb3ad9c317bbde9.html">reduction</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:dir_ed1948a6da781e7f72c597b5619a522d"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_ed1948a6da781e7f72c597b5619a522d.html">thread</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:dir_568e97a0eb81cc0d3daf98cef30c9135"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_568e97a0eb81cc0d3daf98cef30c9135.html">transform</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:dir_6c0b0ac954bdf2d913b6e24246bcb749"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_6c0b0ac954bdf2d913b6e24246bcb749.html">util</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="files"></a>
Files</h2></td></tr>
<tr class="memitem:aligned__buffer_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="aligned__buffer_8h.html">aligned_buffer.h</a> <a href="aligned__buffer_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:aligned__buffer_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">AlignedBuffer is a container for trivially copyable elements suitable for use in unions and shared memory. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:array_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="array_8h.html">array.h</a> <a href="array_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:array_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe to use in a union. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:array__subbyte_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="array__subbyte_8h.html">array_subbyte.h</a> <a href="array__subbyte_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:array__subbyte_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe to use in a union. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:complex_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="complex_8h.html">complex.h</a> <a href="complex_8h_source.html">[code]</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:coord_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="coord_8h.html">coord.h</a> <a href="coord_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:coord_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">A Coord is a coordinate of arbitrary rank into a tensor or matrix. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:core__io_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="core__io_8h.html">core_io.h</a> <a href="core__io_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:core__io_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Helpers for printing cutlass/core objects. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:cutlass_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="cutlass_8h.html">cutlass.h</a> <a href="cutlass_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:cutlass_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Basic include for CUTLASS. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:device__kernel_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="device__kernel_8h.html">device_kernel.h</a> <a href="device__kernel_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:device__kernel_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Template for generic CUTLASS kernel. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:fast__math_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="fast__math_8h.html">fast_math.h</a> <a href="fast__math_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:fast__math_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Math utilities. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:functional_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="functional_8h.html">functional.h</a> <a href="functional_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:functional_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define basic numeric operators with specializations for Array&lt;T, N&gt;. SIMD-ize where possible. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:half_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="half_8h.html">half.h</a> <a href="half_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:half_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines a class for using IEEE half-precision floating-point types in host or device code. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:integer__subbyte_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="integer__subbyte_8h.html">integer_subbyte.h</a> <a href="integer__subbyte_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:integer__subbyte_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines a class for using integer types smaller than one byte in host or device code. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:kernel__launch_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="kernel__launch_8h.html">kernel_launch.h</a> <a href="kernel__launch_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:kernel__launch_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines structures and helpers to launch CUDA kernels within CUTLASS. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:matrix__coord_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="matrix__coord_8h.html">matrix_coord.h</a> <a href="matrix__coord_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:matrix__coord_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines a canonical coordinate for rank=2 matrices offering named indices. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:matrix__shape_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="matrix__shape_8h.html">matrix_shape.h</a> <a href="matrix__shape_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:matrix__shape_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines a Shape template for matrix tiles. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:matrix__traits_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="matrix__traits_8h.html">matrix_traits.h</a> <a href="matrix__traits_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:matrix__traits_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines properties of matrices used to denote layout and operands to GEMM kernels. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:numeric__conversion_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="numeric__conversion_8h.html">numeric_conversion.h</a> <a href="numeric__conversion_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:numeric__conversion_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Boost-like numeric conversion operator for CUTLASS numeric types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:numeric__types_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="numeric__types_8h.html">numeric_types.h</a> <a href="numeric__types_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:numeric__types_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Top-level include for all CUTLASS numeric types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:predicate__vector_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="predicate__vector_8h.html">predicate_vector.h</a> <a href="predicate__vector_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:predicate__vector_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines container classes and iterators for managing a statically sized vector of boolean predicates. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:real_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="real_8h.html">real.h</a> <a href="real_8h_source.html">[code]</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:relatively__equal_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="relatively__equal_8h.html">relatively_equal.h</a> <a href="relatively__equal_8h_source.html">[code]</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:semaphore_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="semaphore_8h.html">semaphore.h</a> <a href="semaphore_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:semaphore_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Implementation of a CTA-wide semaphore for inter-CTA synchronization. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:subbyte__reference_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="subbyte__reference_8h.html">subbyte_reference.h</a> <a href="subbyte__reference_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:subbyte__reference_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides a mechanism for packing and unpacking elements smaller than one byte. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:tensor__coord_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="tensor__coord_8h.html">tensor_coord.h</a> <a href="tensor__coord_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:tensor__coord_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines a canonical coordinate for rank=4 tensors offering named indices. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:tensor__ref_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="tensor__ref_8h.html">tensor_ref.h</a> <a href="tensor__ref_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:tensor__ref_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines a structure containing strides, bounds, and a pointer to tensor data. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:tensor__view_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="tensor__view_8h.html">tensor_view.h</a> <a href="tensor__view_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:tensor__view_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines a structure containing strides and a pointer to tensor data. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:wmma__array_8h"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="wmma__array_8h.html">wmma_array.h</a> <a href="wmma__array_8h_source.html">[code]</a></td></tr>
<tr class="memdesc:wmma__array_8h"><td class="mdescLeft">&#160;</td><td class="mdescRight">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe to use in a union. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
