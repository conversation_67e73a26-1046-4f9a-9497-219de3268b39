<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: fragment_iterator_tensor_op.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_e7fd38dbfb1fb5decd4aa6571e13ec6b.html">warp</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">fragment_iterator_tensor_op.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="fragment__iterator__tensor__op_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__op__policy_8h.html">cutlass/epilogue/warp/tensor_op_policy.h</a>&quot;</span></div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="keyword">namespace </span>warp {</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;  <span class="keyword">typename</span> WarpShape,         </div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;  <span class="keyword">typename</span> OperatorShape,     </div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <span class="keyword">typename</span> OperatorElementC,  </div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  <span class="keyword">typename</span> OperatorFragmentC, </div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;  <span class="keyword">typename</span> Layout             </div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;&gt;</div><div class="line"><a name="l00061"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp.html">   61</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp.html">FragmentIteratorTensorOp</a>;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <span class="keyword">typename</span> WarpShape_,         </div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  <span class="keyword">typename</span> OperatorShape_,     </div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <span class="keyword">typename</span> OperatorElementC_,  </div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  <span class="keyword">typename</span> OperatorFragmentC_  </div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;&gt;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html">   72</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp.html">FragmentIteratorTensorOp</a>&lt;WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor&gt; {</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a3b13525527ec73934e45d168d4963719">   75</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a3b13525527ec73934e45d168d4963719">WarpShape</a> = WarpShape_;</div><div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#aa16054789b2591940031f2dba8030137">   76</a></span>&#160;  <span class="keyword">using</span> OperatorShape = OperatorShape_;</div><div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a1e8556a31ca442cd32f78317bc58f999">   77</a></span>&#160;  <span class="keyword">using</span> OperatorElementC = OperatorElementC_;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a8fc00e4fac9c065d4c0d3635f1c64c44">   78</a></span>&#160;  <span class="keyword">using</span> OperatorFragmentC = OperatorFragmentC_;</div><div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a58f1b1e6cdba518d44a4ce5035d943cb">   79</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a008247244142e3fadbd130bb9c268e24">   81</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">Policy</a> = <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">TensorOpPolicy&lt;WarpShape, OperatorShape, Layout&gt;</a>;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#ac1cbc7d77f60cedb3e7aa88c24ea04b1">Fragment</a> = Array&lt;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    OperatorElementC, </div><div class="line"><a name="l00086"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#ac1cbc7d77f60cedb3e7aa88c24ea04b1">   86</a></span>&#160;    Policy::OperatorCount::kColumn * Policy::kElementsPerAccess&gt;;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a2adfa48a2bfefab2cddd5b2185a6d80b">AccumulatorTile</a> = Array&lt;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    OperatorElementC, </div><div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a2adfa48a2bfefab2cddd5b2185a6d80b">   91</a></span>&#160;    OperatorFragmentC::kElements * Policy::OperatorCount::kRow * Policy::OperatorCount::kColumn&gt;;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00093"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#ab84581ab676ca9159aad956f88b7f9ed">   93</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#ab84581ab676ca9159aad956f88b7f9ed">OutputAccumulatorTile</a> = <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a2adfa48a2bfefab2cddd5b2185a6d80b">AccumulatorTile</a>;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#ae452dfefe60053bdd46a19caaf3ec3f3">   96</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = Policy::kIterations;</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  <span class="keyword">using</span> AccessType = Array&lt;OperatorElementC, Policy::kElementsPerAccess&gt;;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  AccessType <span class="keyword">const</span> *accumulators_;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  <span class="keywordtype">int</span> index_;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00119"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a2fcc41a59fce4e01d3ea7917a73c5ec8">  119</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a2fcc41a59fce4e01d3ea7917a73c5ec8">FragmentIteratorTensorOp</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a2adfa48a2bfefab2cddd5b2185a6d80b">AccumulatorTile</a> <span class="keyword">const</span> &amp;accum): </div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;    accumulators_(reinterpret_cast&lt;AccessType const *&gt;(&amp;accum)), </div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    index_(0) {</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;  }</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#ac638be718341c995d2fb0948eab54d34">  126</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp.html">FragmentIteratorTensorOp</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#ac638be718341c995d2fb0948eab54d34">operator++</a>() {</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;    ++index_;</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  }</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00133"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a91c59ef117cc8d16241b58d5f0c18399">  133</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp.html">FragmentIteratorTensorOp</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a91c59ef117cc8d16241b58d5f0c18399">operator--</a>() {</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;    --index_;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  }</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00140"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#aeb486b8f0cfa33ae7b64cb88c199f312">  140</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#aeb486b8f0cfa33ae7b64cb88c199f312">load</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#ac1cbc7d77f60cedb3e7aa88c24ea04b1">Fragment</a> &amp;frag, <span class="keywordtype">int</span> index_offset = 0)<span class="keyword"> const </span>{</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;    <span class="keywordtype">int</span> index = index_ + index_offset;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    AccessType *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span>AccessType *<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> n = 0; n &lt; Policy::OperatorCount::kColumn; ++n) {</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;      <span class="keywordtype">int</span> accumulator_access_offset = </div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;        index + n * Policy::kAccumulatorColumnStride / Policy::kElementsPerAccess;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;      frag_ptr[n] = accumulators_[accumulator_access_offset];</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    }</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;  }</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;};</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    <span class="keyword">typename</span> WarpShape_,</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    <span class="keyword">typename</span> OperatorShape_,</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;    <span class="keyword">typename</span> OperatorElementC_,</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    <span class="keyword">typename</span> OperatorFragmentC_,</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    <span class="keywordtype">int</span> InterleavedK&gt;</div><div class="line"><a name="l00171"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html">  171</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp.html">FragmentIteratorTensorOp</a>&lt;WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_,</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;                               layout::ColumnMajorInterleaved&lt;InterleavedK&gt;&gt; {</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00174"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#aa3102326f996979df8a479c21706753c">  174</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#aa3102326f996979df8a479c21706753c">WarpShape</a> = WarpShape_;</div><div class="line"><a name="l00175"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#af8e6cbc27d0b043e7a4c8ecd3f709427">  175</a></span>&#160;  <span class="keyword">using</span> OperatorShape = OperatorShape_;</div><div class="line"><a name="l00176"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#ad161c34ceed9c0a4b700cfd7af486373">  176</a></span>&#160;  <span class="keyword">using</span> OperatorElementC = OperatorElementC_;</div><div class="line"><a name="l00177"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#aa21a9f6adb7fdcac7971577918295afd">  177</a></span>&#160;  <span class="keyword">using</span> OperatorFragmentC = OperatorFragmentC_;</div><div class="line"><a name="l00178"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#afdf4bf88b1edd663c25a7a8b1f4465b6">  178</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kInterleavedK = InterleavedK;</div><div class="line"><a name="l00179"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a7178bbf806b6bf01af88d1b551b3745c">  179</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html">Layout</a> = <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html">layout::ColumnMajorInterleaved&lt;kInterleavedK&gt;</a>;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;</div><div class="line"><a name="l00181"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#af682ef0a9152d8f1c34e0d14ee084e0d">  181</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">Policy</a> = <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">TensorOpPolicy&lt;WarpShape, OperatorShape, Layout&gt;</a>;</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a53fe88441ba1f7bbe5cb30ae60eb2be6">Fragment</a> =</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;      Array&lt;OperatorElementC,</div><div class="line"><a name="l00186"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a53fe88441ba1f7bbe5cb30ae60eb2be6">  186</a></span>&#160;            Policy::kElementsPerAccess * InterleavedK / OperatorShape::kN&gt;;</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#aafb2c78a1f680ddc24f77d87c8edf40f">AccumulatorTile</a> =</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;      Array&lt;OperatorElementC, OperatorFragmentC::kElements *</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;                                  Policy::OperatorCount::kRow *</div><div class="line"><a name="l00192"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#aafb2c78a1f680ddc24f77d87c8edf40f">  192</a></span>&#160;                                  Policy::OperatorCount::kColumn&gt;;</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;</div><div class="line"><a name="l00195"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a9326d02f0d226f190c50a621c3fb826e">  195</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = Policy::kIterations;</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;  <span class="keyword">using</span> AccessType =</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;      Array&lt;OperatorElementC, Policy::kElementsPerAccess&gt;;</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;  AccessType <span class="keyword">const</span> *accumulators_;</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;  <span class="keywordtype">int</span> index_;</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00216"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#ad690dd7f54217a7d1e2033e557306e8d">  216</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#ad690dd7f54217a7d1e2033e557306e8d">FragmentIteratorTensorOp</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#aafb2c78a1f680ddc24f77d87c8edf40f">AccumulatorTile</a> <span class="keyword">const</span> &amp;accum)</div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;      : accumulators_(reinterpret_cast&lt;AccessType const *&gt;(&amp;accum)),</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;        index_(0) {}</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00222"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#af51b1e23c36ba486a00eeb614589967e">  222</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp.html">FragmentIteratorTensorOp</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#af51b1e23c36ba486a00eeb614589967e">operator++</a>() {</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;    ++index_;</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;  }</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00229"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a6faf5b99575aac462873322e194dd2d4">  229</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp.html">FragmentIteratorTensorOp</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a6faf5b99575aac462873322e194dd2d4">operator--</a>() {</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;    --index_;</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;  }</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00236"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a24161390822e4b5880f73fccb50826c0">  236</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a24161390822e4b5880f73fccb50826c0">load</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a53fe88441ba1f7bbe5cb30ae60eb2be6">Fragment</a> &amp;frag, <span class="keywordtype">int</span> index_offset = 0)<span class="keyword"> const </span>{</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;    <span class="keywordtype">int</span> index = index_ + index_offset;</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;    AccessType *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span>AccessType *<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> n = 0; n &lt; (InterleavedK / OperatorShape::kN); ++n) {</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;      <span class="keywordtype">int</span> index_m = index % (Policy::OperatorCount::kRow *</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;                             Policy::kIterationsPerInstruction);</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;      <span class="keywordtype">int</span> index_n = index / (Policy::OperatorCount::kRow *</div><div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;                             Policy::kIterationsPerInstruction);</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;      <span class="keywordtype">int</span> accumulator_access_offset =</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;          (index_m / Policy::kIterationsPerInstruction) *</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;              (Policy::OperatorCount::kColumn *</div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;               Policy::kIterationsPerInstruction) +</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;          (index_m % Policy::kIterationsPerInstruction) +</div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;          index_n * (InterleavedK / OperatorShape::kN) *</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;              Policy::kIterationsPerInstruction +</div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;          n * Policy::kIterationsPerInstruction;</div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;</div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;      frag_ptr[n] = accumulators_[accumulator_access_offset];</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;    }</div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;  }</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;};</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;} <span class="comment">// namespace warp</span></div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;</div><div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda_html_a3b13525527ec73934e45d168d4963719"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a3b13525527ec73934e45d168d4963719">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::WarpShape</a></div><div class="ttdeci">WarpShape_ WarpShape</div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:75</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda_html_aeb486b8f0cfa33ae7b64cb88c199f312"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#aeb486b8f0cfa33ae7b64cb88c199f312">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::load</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load(Fragment &amp;frag, int index_offset=0) const </div><div class="ttdoc">Loads a fragment from the referenced part of the accumulator tile. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:140</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="tensor__op__policy_8h_html"><div class="ttname"><a href="tensor__op__policy_8h.html">tensor_op_policy.h</a></div><div class="ttdoc">Defines basic structures needed for implementing the warp-scoped phase of the epilogue. These quantities assume a &amp;#39;column-major&amp;#39; arrangement of TensorOp instructions, of which a row-oriented slice is visible per iteration. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda_html_ab84581ab676ca9159aad956f88b7f9ed"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#ab84581ab676ca9159aad956f88b7f9ed">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::OutputAccumulatorTile</a></div><div class="ttdeci">AccumulatorTile OutputAccumulatorTile</div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:93</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726_html_a24161390822e4b5880f73fccb50826c0"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a24161390822e4b5880f73fccb50826c0">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::load</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load(Fragment &amp;frag, int index_offset=0) const </div><div class="ttdoc">Loads a fragment from the referenced part of the accumulator tile. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:236</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726_html_a6faf5b99575aac462873322e194dd2d4"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a6faf5b99575aac462873322e194dd2d4">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorTensorOp &amp; operator--()</div><div class="ttdoc">Decrements. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:229</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726_html_aa3102326f996979df8a479c21706753c"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#aa3102326f996979df8a479c21706753c">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::WarpShape</a></div><div class="ttdeci">WarpShape_ WarpShape</div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:174</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda_html_a2adfa48a2bfefab2cddd5b2185a6d80b"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a2adfa48a2bfefab2cddd5b2185a6d80b">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::AccumulatorTile</a></div><div class="ttdeci">Array&lt; OperatorElementC, OperatorFragmentC::kElements *Policy::OperatorCount::kRow *Policy::OperatorCount::kColumn &gt; AccumulatorTile</div><div class="ttdoc">This is the complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:91</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda_html_ac1cbc7d77f60cedb3e7aa88c24ea04b1"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#ac1cbc7d77f60cedb3e7aa88c24ea04b1">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::Fragment</a></div><div class="ttdeci">Array&lt; OperatorElementC, Policy::OperatorCount::kColumn *Policy::kElementsPerAccess &gt; Fragment</div><div class="ttdoc">This is the fragment size produced by one access of the iterator. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:86</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">cutlass::epilogue::warp::TensorOpPolicy</a></div><div class="ttdoc">Policy details related to the epilogue. </div><div class="ttdef"><b>Definition:</b> tensor_op_policy.h:50</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726_html_af51b1e23c36ba486a00eeb614589967e"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#af51b1e23c36ba486a00eeb614589967e">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorTensorOp &amp; operator++()</div><div class="ttdoc">Increments. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:222</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda_html_a2fcc41a59fce4e01d3ea7917a73c5ec8"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a2fcc41a59fce4e01d3ea7917a73c5ec8">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::FragmentIteratorTensorOp</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorTensorOp(AccumulatorTile const &amp;accum)</div><div class="ttdoc">Constructs an iterator. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:119</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726_html_ad690dd7f54217a7d1e2033e557306e8d"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#ad690dd7f54217a7d1e2033e557306e8d">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::FragmentIteratorTensorOp</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorTensorOp(AccumulatorTile const &amp;accum)</div><div class="ttdoc">Constructs an iterator. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:216</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp.html">cutlass::epilogue::warp::FragmentIteratorTensorOp</a></div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:61</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726_html_a53fe88441ba1f7bbe5cb30ae60eb2be6"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#a53fe88441ba1f7bbe5cb30ae60eb2be6">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::Fragment</a></div><div class="ttdeci">Array&lt; OperatorElementC, Policy::kElementsPerAccess *InterleavedK/OperatorShape::kN &gt; Fragment</div><div class="ttdoc">This is the fragment size produced by one access of the iterator. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:186</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda_html_ac638be718341c995d2fb0948eab54d34"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#ac638be718341c995d2fb0948eab54d34">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorTensorOp &amp; operator++()</div><div class="ttdoc">Increments. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:126</div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorInterleaved_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html">cutlass::layout::ColumnMajorInterleaved</a></div><div class="ttdef"><b>Definition:</b> layout/matrix.h:343</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726_html_aafb2c78a1f680ddc24f77d87c8edf40f"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_e459aab140a2ce78336e584f95886726.html#aafb2c78a1f680ddc24f77d87c8edf40f">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt; &gt;::AccumulatorTile</a></div><div class="ttdeci">Array&lt; OperatorElementC, OperatorFragmentC::kElements *Policy::OperatorCount::kRow *Policy::OperatorCount::kColumn &gt; AccumulatorTile</div><div class="ttdoc">This is the complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:192</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda_html_a91c59ef117cc8d16241b58d5f0c18399"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorTensorOp_3_01WarpShape___00_01OperatorShape_5e78dabe303f20d76b00c600aab61eda.html#a91c59ef117cc8d16241b58d5f0c18399">cutlass::epilogue::warp::FragmentIteratorTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorTensorOp &amp; operator--()</div><div class="ttdoc">Decrements. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_tensor_op.h:133</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
