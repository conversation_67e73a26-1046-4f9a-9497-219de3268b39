<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: fragment_iterator_complex_tensor_op.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_e7fd38dbfb1fb5decd4aa6571e13ec6b.html">warp</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">fragment_iterator_complex_tensor_op.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="fragment__iterator__complex__tensor__op_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__op__policy_8h.html">cutlass/epilogue/warp/tensor_op_policy.h</a>&quot;</span></div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00049"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1epilogue_1_1warp.html">   49</a></span>&#160;<span class="keyword">namespace </span>warp {</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;  <span class="keyword">typename</span> WarpShape,         </div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;  <span class="keyword">typename</span> OperatorShape,     </div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <span class="keyword">typename</span> OperatorElementC,  </div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  <span class="keyword">typename</span> OperatorFragmentC, </div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;  <span class="keyword">typename</span> Layout             </div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;&gt;</div><div class="line"><a name="l00061"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp.html">   61</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp.html">FragmentIteratorComplexTensorOp</a>;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  <span class="keyword">typename</span> WarpShape_,         </div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <span class="keyword">typename</span> OperatorShape_,     </div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  <span class="keyword">typename</span> OperatorElementC_,  </div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  <span class="keyword">typename</span> OperatorFragmentC_  </div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;&gt;</div><div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html">   73</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp.html">FragmentIteratorComplexTensorOp</a>&lt;WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor&gt; {</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a7b0c63af523c3ad2f27989c4521030a7">   76</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a7b0c63af523c3ad2f27989c4521030a7">WarpShape</a> = WarpShape_;</div><div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#af6285ce051341ad04215a545fbd995a5">   77</a></span>&#160;  <span class="keyword">using</span> OperatorShape = OperatorShape_;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a899e647b0de1eee2c842fdcea667ab56">   78</a></span>&#160;  <span class="keyword">using</span> OperatorElementC = OperatorElementC_;</div><div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#af187a75e35b81d0a25494612df5c4ee2">   79</a></span>&#160;  <span class="keyword">using</span> OperatorFragmentC = OperatorFragmentC_;</div><div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a2c2b69f22e14ea9230d90b797b21e842">   80</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div><div class="line"><a name="l00082"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a9230b73de63fcf673cc1e9125113d9c4">   82</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">Policy</a> = <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">TensorOpPolicy&lt;WarpShape, OperatorShape, Layout&gt;</a>;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ac611843b0222b7f7538ebe8f06f48b6a">Fragment</a> = Array&lt;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    <a class="code" href="classcutlass_1_1complex.html">complex&lt;OperatorElementC&gt;</a>, </div><div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ac611843b0222b7f7538ebe8f06f48b6a">   87</a></span>&#160;    Policy::OperatorCount::kColumn * Policy::kElementsPerAccess&gt;;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00089"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a33779b0965d1ce6810dc73638b80f19a">   89</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRealIndex = 0;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#acbb82d0594bc94dc0fe4c6bb4c9a22e4">   92</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kImaginaryIndex = </div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    OperatorFragmentC::kElements * Policy::OperatorCount::kRow * Policy::OperatorCount::kColumn;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a61720e463645c8e4b32021b07fe27a45">   96</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a61720e463645c8e4b32021b07fe27a45">AccumulatorTile</a> = Array&lt;OperatorElementC, 2 * kImaginaryIndex&gt;;</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div><div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ac1e3ba42a79330b91589676af041efff">   99</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ac1e3ba42a79330b91589676af041efff">OutputAccumulatorTile</a> = Array&lt;complex&lt;OperatorElementC&gt;, kImaginaryIndex&gt;;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div><div class="line"><a name="l00102"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ad7012975aa28a6cdc90a1f28688693ec">  102</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = Policy::kIterations;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;  <span class="keyword">using</span> AccessType = Array&lt;OperatorElementC, Policy::kElementsPerAccess&gt;;</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;  <span class="keyword">using</span> FragmentAccessType = Array&lt;complex&lt;OperatorElementC&gt;, Policy::kElementsPerAccess&gt;;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;  AccessType <span class="keyword">const</span> *accumulators_;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;  <span class="keywordtype">int</span> index_;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#acad4794bc84fbe582f58b4328c0d84a4">  127</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#acad4794bc84fbe582f58b4328c0d84a4">FragmentIteratorComplexTensorOp</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a61720e463645c8e4b32021b07fe27a45">AccumulatorTile</a> <span class="keyword">const</span> &amp;accum): </div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    accumulators_(reinterpret_cast&lt;AccessType const *&gt;(&amp;accum)), </div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    index_(0) {</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  }</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00135"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ac18b19d15654fbbae90af8de4f5c4540">  135</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp.html">FragmentIteratorComplexTensorOp</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ac18b19d15654fbbae90af8de4f5c4540">operator++</a>() {</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;    ++index_;</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  }</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00142"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a5ec88193a070b08cfe7b0b9bfed042f8">  142</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp.html">FragmentIteratorComplexTensorOp</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a5ec88193a070b08cfe7b0b9bfed042f8">operator--</a>() {</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    --index_;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;  }</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00149"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ad97f38185da47d8af981397b23f07cd7">  149</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ad97f38185da47d8af981397b23f07cd7">load</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ac611843b0222b7f7538ebe8f06f48b6a">Fragment</a> &amp;frag, <span class="keywordtype">int</span> index_offset = 0)<span class="keyword"> const </span>{</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    <span class="keywordtype">int</span> index = index_ + index_offset;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    FragmentAccessType *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span>FragmentAccessType *<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> n = 0; n &lt; Policy::OperatorCount::kColumn; ++n) {</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;      <span class="keywordtype">int</span> accumulator_access_offset = </div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        index + n * Policy::kAccumulatorColumnStride / Policy::kElementsPerAccess;</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;      <span class="keyword">auto</span> <span class="keyword">const</span> &amp; real_accum_array = accumulators_[accumulator_access_offset + kRealIndex];</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;      <span class="keyword">auto</span> <span class="keyword">const</span> &amp; imag_accum_array = accumulators_[accumulator_access_offset + kImaginaryIndex / Policy::kElementsPerAccess];</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;      <span class="comment">// Pack real and imaginary parts into a structure. This is likely to result in MOVs</span></div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; Policy::kElementsPerAccess; ++i) {</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        frag_ptr[n][i].real() = real_accum_array[i];</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;        frag_ptr[n][i].imag() = imag_accum_array[i]; </div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;      }</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    }</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;  }</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;};</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;} <span class="comment">// namespace warp</span></div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;</div><div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8_html_ac611843b0222b7f7538ebe8f06f48b6a"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ac611843b0222b7f7538ebe8f06f48b6a">cutlass::epilogue::warp::FragmentIteratorComplexTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::Fragment</a></div><div class="ttdeci">Array&lt; complex&lt; OperatorElementC &gt;, Policy::OperatorCount::kColumn *Policy::kElementsPerAccess &gt; Fragment</div><div class="ttdoc">This is the fragment size produced by one access of the iterator. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_complex_tensor_op.h:87</div></div>
<div class="ttc" id="tensor__op__policy_8h_html"><div class="ttname"><a href="tensor__op__policy_8h.html">tensor_op_policy.h</a></div><div class="ttdoc">Defines basic structures needed for implementing the warp-scoped phase of the epilogue. These quantities assume a &amp;#39;column-major&amp;#39; arrangement of TensorOp instructions, of which a row-oriented slice is visible per iteration. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8_html_a61720e463645c8e4b32021b07fe27a45"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a61720e463645c8e4b32021b07fe27a45">cutlass::epilogue::warp::FragmentIteratorComplexTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::AccumulatorTile</a></div><div class="ttdeci">Array&lt; OperatorElementC, 2 *kImaginaryIndex &gt; AccumulatorTile</div><div class="ttdoc">This is the complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_complex_tensor_op.h:96</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8_html_ac18b19d15654fbbae90af8de4f5c4540"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ac18b19d15654fbbae90af8de4f5c4540">cutlass::epilogue::warp::FragmentIteratorComplexTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorComplexTensorOp &amp; operator++()</div><div class="ttdoc">Increments. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_complex_tensor_op.h:135</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8_html_acad4794bc84fbe582f58b4328c0d84a4"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#acad4794bc84fbe582f58b4328c0d84a4">cutlass::epilogue::warp::FragmentIteratorComplexTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::FragmentIteratorComplexTensorOp</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorComplexTensorOp(AccumulatorTile const &amp;accum)</div><div class="ttdoc">Constructs an iterator. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_complex_tensor_op.h:127</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8_html_a7b0c63af523c3ad2f27989c4521030a7"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a7b0c63af523c3ad2f27989c4521030a7">cutlass::epilogue::warp::FragmentIteratorComplexTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::WarpShape</a></div><div class="ttdeci">WarpShape_ WarpShape</div><div class="ttdef"><b>Definition:</b> fragment_iterator_complex_tensor_op.h:76</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8_html_a5ec88193a070b08cfe7b0b9bfed042f8"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#a5ec88193a070b08cfe7b0b9bfed042f8">cutlass::epilogue::warp::FragmentIteratorComplexTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorComplexTensorOp &amp; operator--()</div><div class="ttdoc">Decrements. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_complex_tensor_op.h:142</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp.html">cutlass::epilogue::warp::FragmentIteratorComplexTensorOp</a></div><div class="ttdef"><b>Definition:</b> fragment_iterator_complex_tensor_op.h:61</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">cutlass::epilogue::warp::TensorOpPolicy</a></div><div class="ttdoc">Policy details related to the epilogue. </div><div class="ttdef"><b>Definition:</b> tensor_op_policy.h:50</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8_html_ac1e3ba42a79330b91589676af041efff"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ac1e3ba42a79330b91589676af041efff">cutlass::epilogue::warp::FragmentIteratorComplexTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::OutputAccumulatorTile</a></div><div class="ttdeci">Array&lt; complex&lt; OperatorElementC &gt;, kImaginaryIndex &gt; OutputAccumulatorTile</div><div class="ttdoc">This is the complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_complex_tensor_op.h:99</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8_html_ad97f38185da47d8af981397b23f07cd7"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorComplexTensorOp_3_01WarpShape___00_01Operato8cf03c624cf3210c71b7cbd580b080f8.html#ad97f38185da47d8af981397b23f07cd7">cutlass::epilogue::warp::FragmentIteratorComplexTensorOp&lt; WarpShape_, OperatorShape_, OperatorElementC_, OperatorFragmentC_, layout::RowMajor &gt;::load</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load(Fragment &amp;frag, int index_offset=0) const </div><div class="ttdoc">Loads a fragment from the referenced part of the accumulator tile. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_complex_tensor_op.h:149</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="classcutlass_1_1complex_html"><div class="ttname"><a href="classcutlass_1_1complex.html">cutlass::complex</a></div><div class="ttdef"><b>Definition:</b> complex.h:92</div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
