<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: functional.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">functional.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="functional_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="complex_8h.html">cutlass/complex.h</a>&quot;</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="half_8h.html">cutlass/half.h</a>&quot;</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="structcutlass_1_1plus.html">   46</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1plus.html">plus</a> {</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00048"></a><span class="lineno"><a class="line" href="structcutlass_1_1plus.html#a95661210dd2176c4b8ab86ffc3d5eb50">   48</a></span>&#160;  T <a class="code" href="structcutlass_1_1plus.html#a95661210dd2176c4b8ab86ffc3d5eb50">operator()</a>(T lhs, T <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;    lhs += rhs;</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;    <span class="keywordflow">return</span> lhs;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  }</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;};</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="structcutlass_1_1minus.html">   55</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1minus.html">minus</a> {</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="structcutlass_1_1minus.html#aa0db9bb7741aa930c9a52e795d12f227">   57</a></span>&#160;  T <a class="code" href="structcutlass_1_1minus.html#aa0db9bb7741aa930c9a52e795d12f227">operator()</a>(T lhs, T <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;    lhs -= rhs;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;    <span class="keywordflow">return</span> lhs;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;  }</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;};</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00064"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiplies.html">   64</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1multiplies.html">multiplies</a> {</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiplies.html#adff981528415c4c40b251a3401e642ad">   66</a></span>&#160;  T <a class="code" href="structcutlass_1_1multiplies.html#adff981528415c4c40b251a3401e642ad">operator()</a>(T lhs, T <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;    lhs *= rhs;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;    <span class="keywordflow">return</span> lhs;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  }</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;};</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="structcutlass_1_1divides.html">   73</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1divides.html">divides</a> {</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="structcutlass_1_1divides.html#a45c044899c466720712fa3a55dab57f1">   75</a></span>&#160;  T <a class="code" href="structcutlass_1_1divides.html#a45c044899c466720712fa3a55dab57f1">operator()</a>(T lhs, T <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;    lhs /= rhs;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;    <span class="keywordflow">return</span> lhs;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  }</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;};</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00083"></a><span class="lineno"><a class="line" href="structcutlass_1_1negate.html">   83</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1negate.html">negate</a> {</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="structcutlass_1_1negate.html#a63ff0d5bd31a54bcee8e1512182c8253">   85</a></span>&#160;  T <a class="code" href="structcutlass_1_1negate.html#a63ff0d5bd31a54bcee8e1512182c8253">operator()</a>(T lhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    <span class="keywordflow">return</span> -lhs;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;  }</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;};</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> A, <span class="keyword">typename</span> B = A, <span class="keyword">typename</span> C = A&gt;</div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add.html">   92</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1multiply__add.html">multiply_add</a> {</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add.html#a842a468a8d77bda4b789fb9248926655">   94</a></span>&#160;  C <a class="code" href="structcutlass_1_1multiply__add.html#a842a468a8d77bda4b789fb9248926655">operator()</a>(A <span class="keyword">const</span> &amp;a, B <span class="keyword">const</span> &amp;b, C <span class="keyword">const</span> &amp;c)<span class="keyword"> const </span>{</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;    <span class="keywordflow">return</span> C(a) * C(b) + c;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;  }</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;};</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00101"></a><span class="lineno"><a class="line" href="structcutlass_1_1xor__add.html">  101</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1xor__add.html">xor_add</a> {</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="structcutlass_1_1xor__add.html#a2bda9d781aee02f9aa90c75071a73f98">  103</a></span>&#160;  T <a class="code" href="structcutlass_1_1xor__add.html#a2bda9d781aee02f9aa90c75071a73f98">operator()</a>(T <span class="keyword">const</span> &amp;a, T <span class="keyword">const</span> &amp;b, T <span class="keyword">const</span> &amp;c)<span class="keyword"> const </span>{</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    <span class="keywordflow">return</span> ((a ^ b) + c);</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;  }</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;};</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="comment">// Partial specialization for complex&lt;T&gt; to target four scalar fused multiply-adds.</span></div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00116"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01complex_3_01T_01_4_00_01complex_3_01T_01_4_00_01complex_3_01T_01_4_01_4.html">  116</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1multiply__add.html">multiply_add</a>&lt;<a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;T&gt;, <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;T&gt;, <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;T&gt;&gt; {</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00118"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01complex_3_01T_01_4_00_01complex_3_01T_01_4_00_01complex_3_01T_01_4_01_4.html#af46b43d672441fccd96c1aa3b89886fa">  118</a></span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a> <a class="code" href="structcutlass_1_1multiply__add_3_01complex_3_01T_01_4_00_01complex_3_01T_01_4_00_01complex_3_01T_01_4_01_4.html#af46b43d672441fccd96c1aa3b89886fa">operator()</a>(</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;    <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a> <span class="keyword">const</span> &amp;a, </div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;    <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a> <span class="keyword">const</span> &amp;b, </div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a> <span class="keyword">const</span> &amp;c)<span class="keyword"> const </span>{</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;    T <a class="code" href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">real</a> = c.<a class="code" href="classcutlass_1_1complex.html#afaeeace8c2987047400b83973e196aeb">real</a>();</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;    T <a class="code" href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">imag</a> = c.<a class="code" href="classcutlass_1_1complex.html#aa2c8964e10eeaa99b2d1a18cdcbd0105">imag</a>();</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;    real += a.<a class="code" href="classcutlass_1_1complex.html#afaeeace8c2987047400b83973e196aeb">real</a>() * b.<a class="code" href="classcutlass_1_1complex.html#afaeeace8c2987047400b83973e196aeb">real</a>();</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;    real += -a.<a class="code" href="classcutlass_1_1complex.html#aa2c8964e10eeaa99b2d1a18cdcbd0105">imag</a>() * b.<a class="code" href="classcutlass_1_1complex.html#aa2c8964e10eeaa99b2d1a18cdcbd0105">imag</a>();</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    imag += a.<a class="code" href="classcutlass_1_1complex.html#afaeeace8c2987047400b83973e196aeb">real</a>() * b.<a class="code" href="classcutlass_1_1complex.html#aa2c8964e10eeaa99b2d1a18cdcbd0105">imag</a>();</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    imag += a.<a class="code" href="classcutlass_1_1complex.html#aa2c8964e10eeaa99b2d1a18cdcbd0105">imag</a> () * b.<a class="code" href="classcutlass_1_1complex.html#afaeeace8c2987047400b83973e196aeb">real</a>();</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a>{</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;      <a class="code" href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">real</a>,</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;      imag</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;    };</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  }</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;};</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00140"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01complex_3_01T_01_4_00_01T_00_01complex_3_01T_01_4_01_4.html">  140</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1multiply__add.html">multiply_add</a>&lt;<a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;T&gt;, T, <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;T&gt;&gt; {</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00142"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01complex_3_01T_01_4_00_01T_00_01complex_3_01T_01_4_01_4.html#a28aeeb385a3a47fb72a3d6f421c35294">  142</a></span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a> <a class="code" href="structcutlass_1_1multiply__add_3_01complex_3_01T_01_4_00_01T_00_01complex_3_01T_01_4_01_4.html#a28aeeb385a3a47fb72a3d6f421c35294">operator()</a>(</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a> <span class="keyword">const</span> &amp;a, </div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    T <span class="keyword">const</span> &amp;b, </div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;    <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a> <span class="keyword">const</span> &amp;c)<span class="keyword"> const </span>{</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;    T <a class="code" href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">real</a> = c.<a class="code" href="classcutlass_1_1complex.html#afaeeace8c2987047400b83973e196aeb">real</a>();</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    T <a class="code" href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">imag</a> = c.<a class="code" href="classcutlass_1_1complex.html#aa2c8964e10eeaa99b2d1a18cdcbd0105">imag</a>();</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;    real += a.<a class="code" href="classcutlass_1_1complex.html#afaeeace8c2987047400b83973e196aeb">real</a>() * b;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    imag += a.<a class="code" href="classcutlass_1_1complex.html#aa2c8964e10eeaa99b2d1a18cdcbd0105">imag</a> () * b;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a>{</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;      <a class="code" href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">real</a>,</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;      imag</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    };</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;  }</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;};</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00162"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01T_00_01complex_3_01T_01_4_00_01complex_3_01T_01_4_01_4.html">  162</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1multiply__add.html">multiply_add</a>&lt;T, <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;T&gt;, <a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;T&gt;&gt; {</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00164"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01T_00_01complex_3_01T_01_4_00_01complex_3_01T_01_4_01_4.html#a24b5049dfd834f3a84537b3fceba4e65">  164</a></span>&#160;  <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a> <a class="code" href="structcutlass_1_1multiply__add_3_01T_00_01complex_3_01T_01_4_00_01complex_3_01T_01_4_01_4.html#a24b5049dfd834f3a84537b3fceba4e65">operator()</a>(</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;    T <span class="keyword">const</span> &amp;a, </div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;    <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a> <span class="keyword">const</span> &amp;b, </div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;    <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a> <span class="keyword">const</span> &amp;c)<span class="keyword"> const </span>{</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;    T <a class="code" href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">real</a> = c.<a class="code" href="classcutlass_1_1complex.html#afaeeace8c2987047400b83973e196aeb">real</a>();</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    T <a class="code" href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">imag</a> = c.<a class="code" href="classcutlass_1_1complex.html#aa2c8964e10eeaa99b2d1a18cdcbd0105">imag</a>();</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;    real += a * b.<a class="code" href="classcutlass_1_1complex.html#afaeeace8c2987047400b83973e196aeb">real</a>();</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;    imag += a * b.<a class="code" href="classcutlass_1_1complex.html#aa2c8964e10eeaa99b2d1a18cdcbd0105">imag</a>();</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;T&gt;</a>{</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;      <a class="code" href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">real</a>,</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;      imag</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    };</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;  }</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;};</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;<span class="comment">// Partial specializations for Array&lt;T, N&gt;</span></div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00189"></a><span class="lineno"><a class="line" href="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4.html">  189</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1plus.html">plus</a>&lt;Array&lt;T, N&gt;&gt; {</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00191"></a><span class="lineno"><a class="line" href="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4.html#a93dda0b911aabecf4a115ec7692ff5bd">  191</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4.html#a93dda0b911aabecf4a115ec7692ff5bd">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;    </div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;    <a class="code" href="structcutlass_1_1plus.html">plus&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;      result[i] = scalar_op(lhs[i], rhs[i]);</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;    }</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;  }</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00205"></a><span class="lineno"><a class="line" href="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4.html#a78e99733c048338faac559ce6e33f643">  205</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4.html#a78e99733c048338faac559ce6e33f643">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs, T <span class="keyword">const</span> &amp;scalar)<span class="keyword"> const </span>{</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;    </div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;    <a class="code" href="structcutlass_1_1plus.html">plus&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;      result[i] = scalar_op(lhs[i], scalar);</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;    }</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;  }</div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00219"></a><span class="lineno"><a class="line" href="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4.html#a94e077e7e186dad0c4ccb8d12bf47ffc">  219</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4.html#a94e077e7e186dad0c4ccb8d12bf47ffc">operator()</a>( T <span class="keyword">const</span> &amp;scalar, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;    </div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;    <a class="code" href="structcutlass_1_1plus.html">plus&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;      result[i] = scalar_op(scalar, rhs[i]);</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;    }</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;  }</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;};</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00235"></a><span class="lineno"><a class="line" href="structcutlass_1_1maximum.html">  235</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1maximum.html">maximum</a> {</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00238"></a><span class="lineno"><a class="line" href="structcutlass_1_1maximum.html#a5425d2fc8a8028241332554378e980d7">  238</a></span>&#160;  T <a class="code" href="structcutlass_1_1maximum.html#a5425d2fc8a8028241332554378e980d7">operator()</a>(T <span class="keyword">const</span> &amp;lhs, T <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;    <span class="keywordflow">return</span> (lhs &lt; rhs ? rhs : lhs);</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;  }</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;};</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00244"></a><span class="lineno"><a class="line" href="structcutlass_1_1maximum_3_01float_01_4.html">  244</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1maximum.html">maximum</a>&lt;float&gt; {</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00246"></a><span class="lineno"><a class="line" href="structcutlass_1_1maximum_3_01float_01_4.html#adf9daaeef5c378bb6036c62c3eed9b3e">  246</a></span>&#160;  <span class="keywordtype">float</span> <a class="code" href="structcutlass_1_1maximum_3_01float_01_4.html#adf9daaeef5c378bb6036c62c3eed9b3e">operator()</a>(<span class="keywordtype">float</span> <span class="keyword">const</span> &amp;lhs, <span class="keywordtype">float</span> <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;    <span class="keywordflow">return</span> fmaxf(lhs, rhs);</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;  }</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;};</div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00252"></a><span class="lineno"><a class="line" href="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4.html">  252</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1maximum.html">maximum</a>&lt;Array&lt;T, N&gt;&gt; {</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;</div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00255"></a><span class="lineno"><a class="line" href="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4.html#af02b086d2775d769b9675de752a5351c">  255</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4.html#af02b086d2775d769b9675de752a5351c">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;    </div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;    <a class="code" href="structcutlass_1_1maximum.html">maximum&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;      result[i] = scalar_op(lhs[i], rhs[i]);</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;    }</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;</div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;  }</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;</div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00269"></a><span class="lineno"><a class="line" href="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4.html#a8ae39f34fbeac5fc367503206b7074b0">  269</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4.html#a8ae39f34fbeac5fc367503206b7074b0">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs, T <span class="keyword">const</span> &amp;scalar)<span class="keyword"> const </span>{</div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;    </div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;    <a class="code" href="structcutlass_1_1maximum.html">maximum&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;</div><div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;      result[i] = scalar_op(lhs[i], scalar);</div><div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;    }</div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;  }</div><div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00283"></a><span class="lineno"><a class="line" href="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4.html#a1949655a2c6aa18369f868060651fbd4">  283</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4.html#a1949655a2c6aa18369f868060651fbd4">operator()</a>( T <span class="keyword">const</span> &amp;scalar, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;    </div><div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;    <a class="code" href="structcutlass_1_1maximum.html">maximum&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;</div><div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;      result[i] = scalar_op(scalar, rhs[i]);</div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;    }</div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;</div><div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;  }</div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;};</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;</div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00298"></a><span class="lineno"><a class="line" href="structcutlass_1_1minimum.html">  298</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1minimum.html">minimum</a> {</div><div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;</div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00301"></a><span class="lineno"><a class="line" href="structcutlass_1_1minimum.html#a15eb1ef96e29e5230ee59e89beadfa6b">  301</a></span>&#160;  T <a class="code" href="structcutlass_1_1minimum.html#a15eb1ef96e29e5230ee59e89beadfa6b">operator()</a>(T <span class="keyword">const</span> &amp;lhs, T <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;    <span class="keywordflow">return</span> (rhs &lt; lhs ? rhs : lhs);</div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;  }</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;};</div><div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;</div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00307"></a><span class="lineno"><a class="line" href="structcutlass_1_1minimum_3_01float_01_4.html">  307</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1minimum.html">minimum</a>&lt;float&gt; {</div><div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00309"></a><span class="lineno"><a class="line" href="structcutlass_1_1minimum_3_01float_01_4.html#a13bac6699851e89e63b7b852983d2212">  309</a></span>&#160;  <span class="keywordtype">float</span> <a class="code" href="structcutlass_1_1minimum_3_01float_01_4.html#a13bac6699851e89e63b7b852983d2212">operator()</a>(<span class="keywordtype">float</span> <span class="keyword">const</span> &amp;lhs, <span class="keywordtype">float</span> <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;    <span class="keywordflow">return</span> fminf(lhs, rhs);</div><div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;  }</div><div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;};</div><div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;</div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00315"></a><span class="lineno"><a class="line" href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html">  315</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1minimum.html">minimum</a>&lt;Array&lt;T, N&gt;&gt; {</div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;</div><div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00318"></a><span class="lineno"><a class="line" href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html#a4b42227184cb7c796460062c46a84b57">  318</a></span>&#160;  <span class="keyword">static</span> T <a class="code" href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html#a4b42227184cb7c796460062c46a84b57">scalar_op</a>(T <span class="keyword">const</span> &amp;lhs, T <span class="keyword">const</span> &amp;rhs) {</div><div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;    <span class="keywordflow">return</span> (rhs &lt; lhs ? rhs : lhs);</div><div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;  }</div><div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;</div><div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00323"></a><span class="lineno"><a class="line" href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html#ac2b180299c6c3aa0eead2192c68b71e2">  323</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html#ac2b180299c6c3aa0eead2192c68b71e2">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;    </div><div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;    <a class="code" href="structcutlass_1_1minimum.html">minimum&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;</div><div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;      result[i] = scalar_op(lhs[i], rhs[i]);</div><div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;    }</div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;</div><div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;  }</div><div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;</div><div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00337"></a><span class="lineno"><a class="line" href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html#a4a45f7d2dad800165887d6bec93b16f1">  337</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html#a4a45f7d2dad800165887d6bec93b16f1">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs, T <span class="keyword">const</span> &amp;scalar)<span class="keyword"> const </span>{</div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;    </div><div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;    <a class="code" href="structcutlass_1_1minimum.html">minimum&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;</div><div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;      result[i] = scalar_op(lhs[i], scalar);</div><div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;    }</div><div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;</div><div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;  }</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;</div><div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00351"></a><span class="lineno"><a class="line" href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html#a5dbfcd8f0f61adacea1643f95c57d9e7">  351</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html#a5dbfcd8f0f61adacea1643f95c57d9e7">operator()</a>( T <span class="keyword">const</span> &amp;scalar, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;    </div><div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;    <a class="code" href="structcutlass_1_1minimum.html">minimum&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;</div><div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;      result[i] = scalar_op(scalar, rhs[i]);</div><div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;    }</div><div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;</div><div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;  }</div><div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;};</div><div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;</div><div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00366"></a><span class="lineno"><a class="line" href="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4.html">  366</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1minus.html">minus</a>&lt;Array&lt;T, N&gt;&gt; {</div><div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;</div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00369"></a><span class="lineno"><a class="line" href="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4.html#ac11e165f6f34a5bc47cdecf6b85db1ab">  369</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4.html#ac11e165f6f34a5bc47cdecf6b85db1ab">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;    </div><div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;    <a class="code" href="structcutlass_1_1minus.html">minus&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;</div><div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;      result[i] = scalar_op(lhs[i], rhs[i]);</div><div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;    }</div><div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;</div><div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;  }</div><div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;</div><div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00383"></a><span class="lineno"><a class="line" href="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4.html#af7f0dbf65e39a1780a48472d168fa5c0">  383</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4.html#af7f0dbf65e39a1780a48472d168fa5c0">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs, T <span class="keyword">const</span> &amp;scalar)<span class="keyword"> const </span>{</div><div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;    </div><div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;    <a class="code" href="structcutlass_1_1minus.html">minus&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;</div><div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;      result[i] = scalar_op(lhs[i], scalar);</div><div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;    }</div><div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;</div><div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;  }</div><div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;</div><div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00397"></a><span class="lineno"><a class="line" href="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4.html#a10e1dad45a537e3b92ebd5ce6561069a">  397</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4.html#a10e1dad45a537e3b92ebd5ce6561069a">operator()</a>( T <span class="keyword">const</span> &amp;scalar, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;    </div><div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;    <a class="code" href="structcutlass_1_1minus.html">minus&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;</div><div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;      result[i] = scalar_op(scalar, rhs[i]);</div><div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;    }</div><div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;</div><div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;  }</div><div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;};</div><div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;</div><div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00412"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4.html">  412</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1multiplies.html">multiplies</a>&lt;Array&lt;T, N&gt;&gt; {</div><div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;</div><div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00415"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4.html#a85beeba0468104af0df3c04bebe690d3">  415</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4.html#a85beeba0468104af0df3c04bebe690d3">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;    </div><div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;    <a class="code" href="structcutlass_1_1multiplies.html">multiplies&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;</div><div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;      result[i] = scalar_op(lhs[i], rhs[i]);</div><div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;    }</div><div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;</div><div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;  }</div><div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;</div><div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00429"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4.html#ae13cbdb4ed12e380cf4a858710a7d63f">  429</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4.html#ae13cbdb4ed12e380cf4a858710a7d63f">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs, T <span class="keyword">const</span> &amp;scalar)<span class="keyword"> const </span>{</div><div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;    </div><div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;    <a class="code" href="structcutlass_1_1multiplies.html">multiplies&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;</div><div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;      result[i] = scalar_op(lhs[i], scalar);</div><div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;    }</div><div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;</div><div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;  }</div><div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;</div><div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00443"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4.html#ad911e958e6fa2a43ecb8c0b004f2d7ab">  443</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4.html#ad911e958e6fa2a43ecb8c0b004f2d7ab">operator()</a>( T <span class="keyword">const</span> &amp;scalar, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;    </div><div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;    <a class="code" href="structcutlass_1_1multiplies.html">multiplies&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;</div><div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;      result[i] = scalar_op(scalar, rhs[i]);</div><div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;    }</div><div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;</div><div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;  }</div><div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;};</div><div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;</div><div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00458"></a><span class="lineno"><a class="line" href="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4.html">  458</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1divides.html">divides</a>&lt;Array&lt;T, N&gt;&gt; {</div><div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;</div><div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00461"></a><span class="lineno"><a class="line" href="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4.html#abe16d29dc2547723268d6a0a204550f4">  461</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4.html#abe16d29dc2547723268d6a0a204550f4">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;    </div><div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;    <a class="code" href="structcutlass_1_1divides.html">divides&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;</div><div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160;      result[i] = scalar_op(lhs[i], rhs[i]);</div><div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;    }</div><div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;</div><div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;  }</div><div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;</div><div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00475"></a><span class="lineno"><a class="line" href="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4.html#ac21715e6d4b6cfbdbdfb8cb640694933">  475</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4.html#ac21715e6d4b6cfbdbdfb8cb640694933">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs, T <span class="keyword">const</span> &amp;scalar)<span class="keyword"> const </span>{</div><div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;    </div><div class="line"><a name="l00477"></a><span class="lineno">  477</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;    <a class="code" href="structcutlass_1_1divides.html">divides&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160;</div><div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;      result[i] = scalar_op(lhs[i], scalar);</div><div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;    }</div><div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;</div><div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;  }</div><div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;</div><div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00489"></a><span class="lineno"><a class="line" href="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4.html#af0c3eaffe841ec2a2a8e93f10dafed1a">  489</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4.html#af0c3eaffe841ec2a2a8e93f10dafed1a">operator()</a>( T <span class="keyword">const</span> &amp;scalar, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;    </div><div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;    <a class="code" href="structcutlass_1_1divides.html">divides&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;</div><div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;      result[i] = scalar_op(scalar, rhs[i]);</div><div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;    }</div><div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;</div><div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;  }</div><div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;};</div><div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;</div><div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;</div><div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00505"></a><span class="lineno"><a class="line" href="structcutlass_1_1negate_3_01Array_3_01T_00_01N_01_4_01_4.html">  505</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1negate.html">negate</a>&lt;Array&lt;T, N&gt;&gt; {</div><div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;</div><div class="line"><a name="l00507"></a><span class="lineno">  507</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00508"></a><span class="lineno"><a class="line" href="structcutlass_1_1negate_3_01Array_3_01T_00_01N_01_4_01_4.html#a96bbedf72574b7657c21661ad866639e">  508</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1negate_3_01Array_3_01T_00_01N_01_4_01_4.html#a96bbedf72574b7657c21661ad866639e">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;lhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00509"></a><span class="lineno">  509</span>&#160;    </div><div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;    <a class="code" href="structcutlass_1_1negate.html">negate&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;</div><div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;      result[i] = scalar_op(lhs[i]);</div><div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;    }</div><div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;</div><div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;  }</div><div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;};</div><div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;</div><div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00524"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4.html">  524</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1multiply__add.html">multiply_add</a>&lt;Array&lt;T, N&gt;, Array&lt;T, N&gt;, Array&lt;T, N&gt;&gt; {</div><div class="line"><a name="l00525"></a><span class="lineno">  525</span>&#160;</div><div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00527"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4.html#a070cba31672b435b1edf16e6ded8c955">  527</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4.html#a070cba31672b435b1edf16e6ded8c955">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;a, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;b, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;c)<span class="keyword"> const </span>{</div><div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;    </div><div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;    <a class="code" href="structcutlass_1_1multiply__add.html">multiply_add&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;</div><div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;      result[i] = scalar_op(a[i], b[i], c[i]);</div><div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;    }</div><div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;</div><div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;  }</div><div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160;</div><div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00541"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4.html#a2c69bb9e0339d9e500dfd0a5307e1dd6">  541</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4.html#a2c69bb9e0339d9e500dfd0a5307e1dd6">operator()</a>(Array&lt;T, N&gt; <span class="keyword">const</span> &amp;a, T <span class="keyword">const</span> &amp;scalar, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;c)<span class="keyword"> const </span>{</div><div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;    </div><div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160;    <a class="code" href="structcutlass_1_1multiply__add.html">multiply_add&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00545"></a><span class="lineno">  545</span>&#160;</div><div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;      result[i] = scalar_op(a[i], scalar, c[i]);</div><div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;    }</div><div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160;</div><div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;  }</div><div class="line"><a name="l00553"></a><span class="lineno">  553</span>&#160;</div><div class="line"><a name="l00554"></a><span class="lineno">  554</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00555"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4.html#a71a7c4fbfd3db644fb3cb2d995820856">  555</a></span>&#160;  Array&lt;T, N&gt; <a class="code" href="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4.html#a71a7c4fbfd3db644fb3cb2d995820856">operator()</a>(T <span class="keyword">const</span> &amp;scalar, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;b, Array&lt;T, N&gt; <span class="keyword">const</span> &amp;c)<span class="keyword"> const </span>{</div><div class="line"><a name="l00556"></a><span class="lineno">  556</span>&#160;    </div><div class="line"><a name="l00557"></a><span class="lineno">  557</span>&#160;    Array&lt;T, N&gt; result;</div><div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;    <a class="code" href="structcutlass_1_1multiply__add.html">multiply_add&lt;T&gt;</a> scalar_op;</div><div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;</div><div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160;      result[i] = scalar_op(scalar, b[i], c[i]);</div><div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160;    }</div><div class="line"><a name="l00564"></a><span class="lineno">  564</span>&#160;</div><div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;  }</div><div class="line"><a name="l00567"></a><span class="lineno">  567</span>&#160;};</div><div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;</div><div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;<span class="comment">// Partial specializations for Array&lt;half_t, N&gt; targeting SIMD instructions in device code.</span></div><div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00574"></a><span class="lineno">  574</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00576"></a><span class="lineno"><a class="line" href="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4.html">  576</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1plus.html">plus</a>&lt;Array&lt;<a class="code" href="structcutlass_1_1half__t.html">half_t</a>, N&gt;&gt; {</div><div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00578"></a><span class="lineno"><a class="line" href="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a7eb77069757fffa01b7fba384c1e4bd2">  578</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a7eb77069757fffa01b7fba384c1e4bd2">operator()</a>(Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp; lhs, Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00580"></a><span class="lineno">  580</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;</div><div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00583"></a><span class="lineno">  583</span>&#160;    __half2 <span class="keyword">const</span> *lhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;    __half2 <span class="keyword">const</span> *rhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;</div><div class="line"><a name="l00586"></a><span class="lineno">  586</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;      result_ptr[i] = __hadd2(lhs_ptr[i], rhs_ptr[i]);</div><div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;    }</div><div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;</div><div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l00592"></a><span class="lineno">  592</span>&#160;      __half <span class="keyword">const</span> *a_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160;      __half <span class="keyword">const</span> *b_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00594"></a><span class="lineno">  594</span>&#160;      __half d_residual = __hadd(a_residual_ptr[N - 1], b_residual_ptr[N - 1]);</div><div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;</div><div class="line"><a name="l00596"></a><span class="lineno">  596</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160;    }</div><div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160;</div><div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00600"></a><span class="lineno">  600</span>&#160;</div><div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160;      result[i] = lhs[i] + rhs[i];</div><div class="line"><a name="l00604"></a><span class="lineno">  604</span>&#160;    }</div><div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160;</div><div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00608"></a><span class="lineno">  608</span>&#160;  }</div><div class="line"><a name="l00609"></a><span class="lineno">  609</span>&#160;</div><div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00611"></a><span class="lineno"><a class="line" href="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a2f1a13f5e00fc7756597dc8861fb9fc4">  611</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a2f1a13f5e00fc7756597dc8861fb9fc4">operator()</a>(<a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const</span> &amp; lhs, Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00612"></a><span class="lineno">  612</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00613"></a><span class="lineno">  613</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;</div><div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00616"></a><span class="lineno">  616</span>&#160;    __half2 lhs_pair = __half2half2(reinterpret_cast&lt;__half const &amp;&gt;(lhs));</div><div class="line"><a name="l00617"></a><span class="lineno">  617</span>&#160;    __half2 <span class="keyword">const</span> *rhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160;</div><div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00620"></a><span class="lineno">  620</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l00621"></a><span class="lineno">  621</span>&#160;      result_ptr[i] = __hadd2(lhs_pair, rhs_ptr[i]);</div><div class="line"><a name="l00622"></a><span class="lineno">  622</span>&#160;    }</div><div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;</div><div class="line"><a name="l00624"></a><span class="lineno">  624</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;      __half <span class="keyword">const</span> *b_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;      __half d_residual = __hadd(reinterpret_cast&lt;__half const &amp;&gt;(lhs), b_residual_ptr[N - 1]);</div><div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160;</div><div class="line"><a name="l00628"></a><span class="lineno">  628</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l00629"></a><span class="lineno">  629</span>&#160;    }</div><div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160;</div><div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;</div><div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00634"></a><span class="lineno">  634</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00635"></a><span class="lineno">  635</span>&#160;      result[i] = lhs + rhs[i];</div><div class="line"><a name="l00636"></a><span class="lineno">  636</span>&#160;    }</div><div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;</div><div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00640"></a><span class="lineno">  640</span>&#160;  }</div><div class="line"><a name="l00641"></a><span class="lineno">  641</span>&#160;</div><div class="line"><a name="l00642"></a><span class="lineno">  642</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00643"></a><span class="lineno"><a class="line" href="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a0af810657ec58a868d05157e2c819f1f">  643</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a0af810657ec58a868d05157e2c819f1f">operator()</a>(Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp; lhs, <a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00644"></a><span class="lineno">  644</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;</div><div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00648"></a><span class="lineno">  648</span>&#160;    __half2 <span class="keyword">const</span> *lhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00649"></a><span class="lineno">  649</span>&#160;    __half2 rhs_pair = __half2half2(reinterpret_cast&lt;__half const &amp;&gt;(rhs));</div><div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;</div><div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00652"></a><span class="lineno">  652</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l00653"></a><span class="lineno">  653</span>&#160;      result_ptr[i] = __hadd2(lhs_ptr[i], rhs_pair);</div><div class="line"><a name="l00654"></a><span class="lineno">  654</span>&#160;    }</div><div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160;</div><div class="line"><a name="l00656"></a><span class="lineno">  656</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l00657"></a><span class="lineno">  657</span>&#160;      __half <span class="keyword">const</span> *a_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00658"></a><span class="lineno">  658</span>&#160;      __half d_residual = __hadd(a_residual_ptr[N - 1], reinterpret_cast&lt;__half const &amp;&gt;(rhs));</div><div class="line"><a name="l00659"></a><span class="lineno">  659</span>&#160;</div><div class="line"><a name="l00660"></a><span class="lineno">  660</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160;    }</div><div class="line"><a name="l00662"></a><span class="lineno">  662</span>&#160;</div><div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;</div><div class="line"><a name="l00665"></a><span class="lineno">  665</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00666"></a><span class="lineno">  666</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00667"></a><span class="lineno">  667</span>&#160;      result[i] = lhs[i] + rhs;</div><div class="line"><a name="l00668"></a><span class="lineno">  668</span>&#160;    }</div><div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00670"></a><span class="lineno">  670</span>&#160;</div><div class="line"><a name="l00671"></a><span class="lineno">  671</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00672"></a><span class="lineno">  672</span>&#160;  }</div><div class="line"><a name="l00673"></a><span class="lineno">  673</span>&#160;};</div><div class="line"><a name="l00674"></a><span class="lineno">  674</span>&#160;</div><div class="line"><a name="l00675"></a><span class="lineno">  675</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00676"></a><span class="lineno"><a class="line" href="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4.html">  676</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1minus.html">minus</a>&lt;Array&lt;<a class="code" href="structcutlass_1_1half__t.html">half_t</a>, N&gt;&gt; {</div><div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00678"></a><span class="lineno"><a class="line" href="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a76637faf0a196f09e6e70318ac574a4b">  678</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a76637faf0a196f09e6e70318ac574a4b">operator()</a>(Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp; lhs, Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00679"></a><span class="lineno">  679</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;</div><div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00683"></a><span class="lineno">  683</span>&#160;    __half2 <span class="keyword">const</span> *lhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00684"></a><span class="lineno">  684</span>&#160;    __half2 <span class="keyword">const</span> *rhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00685"></a><span class="lineno">  685</span>&#160;</div><div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00687"></a><span class="lineno">  687</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l00688"></a><span class="lineno">  688</span>&#160;      result_ptr[i] = __hsub2(lhs_ptr[i], rhs_ptr[i]);</div><div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;    }</div><div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;</div><div class="line"><a name="l00691"></a><span class="lineno">  691</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;      __half <span class="keyword">const</span> *a_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;      __half <span class="keyword">const</span> *b_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;      __half d_residual = __hsub(a_residual_ptr[N - 1], b_residual_ptr[N - 1]);</div><div class="line"><a name="l00695"></a><span class="lineno">  695</span>&#160;</div><div class="line"><a name="l00696"></a><span class="lineno">  696</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l00697"></a><span class="lineno">  697</span>&#160;    }</div><div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;</div><div class="line"><a name="l00699"></a><span class="lineno">  699</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00700"></a><span class="lineno">  700</span>&#160;</div><div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00703"></a><span class="lineno">  703</span>&#160;      result[i] = lhs[i] - rhs[i];</div><div class="line"><a name="l00704"></a><span class="lineno">  704</span>&#160;    }</div><div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00706"></a><span class="lineno">  706</span>&#160;</div><div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00708"></a><span class="lineno">  708</span>&#160;  }</div><div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;</div><div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00711"></a><span class="lineno"><a class="line" href="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a177841dc0eb55619ce5c252905c366fd">  711</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a177841dc0eb55619ce5c252905c366fd">operator()</a>(<a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const</span> &amp; lhs, Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00712"></a><span class="lineno">  712</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00713"></a><span class="lineno">  713</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00714"></a><span class="lineno">  714</span>&#160;</div><div class="line"><a name="l00715"></a><span class="lineno">  715</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;    __half2 lhs_pair = __half2half2(reinterpret_cast&lt;__half const &amp;&gt;(lhs));</div><div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160;    __half2 <span class="keyword">const</span> *rhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;</div><div class="line"><a name="l00719"></a><span class="lineno">  719</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00720"></a><span class="lineno">  720</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;      result_ptr[i] = __hsub2(lhs_pair, rhs_ptr[i]);</div><div class="line"><a name="l00722"></a><span class="lineno">  722</span>&#160;    }</div><div class="line"><a name="l00723"></a><span class="lineno">  723</span>&#160;</div><div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160;      __half <span class="keyword">const</span> *b_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00726"></a><span class="lineno">  726</span>&#160;      __half d_residual = __hsub(reinterpret_cast&lt;__half const &amp;&gt;(lhs), b_residual_ptr[N - 1]);</div><div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;</div><div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;    }</div><div class="line"><a name="l00730"></a><span class="lineno">  730</span>&#160;</div><div class="line"><a name="l00731"></a><span class="lineno">  731</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00732"></a><span class="lineno">  732</span>&#160;</div><div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00734"></a><span class="lineno">  734</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00735"></a><span class="lineno">  735</span>&#160;      result[i] = lhs - rhs[i];</div><div class="line"><a name="l00736"></a><span class="lineno">  736</span>&#160;    }</div><div class="line"><a name="l00737"></a><span class="lineno">  737</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00738"></a><span class="lineno">  738</span>&#160;</div><div class="line"><a name="l00739"></a><span class="lineno">  739</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00740"></a><span class="lineno">  740</span>&#160;  }</div><div class="line"><a name="l00741"></a><span class="lineno">  741</span>&#160;</div><div class="line"><a name="l00742"></a><span class="lineno">  742</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00743"></a><span class="lineno"><a class="line" href="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a77e216f4b4c22699604ab06d9175bfd1">  743</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a77e216f4b4c22699604ab06d9175bfd1">operator()</a>(Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp; lhs, <a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00744"></a><span class="lineno">  744</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00745"></a><span class="lineno">  745</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00746"></a><span class="lineno">  746</span>&#160;</div><div class="line"><a name="l00747"></a><span class="lineno">  747</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00748"></a><span class="lineno">  748</span>&#160;    __half2 <span class="keyword">const</span> *lhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00749"></a><span class="lineno">  749</span>&#160;    __half2 rhs_pair = __half2half2(reinterpret_cast&lt;__half const &amp;&gt;(rhs));</div><div class="line"><a name="l00750"></a><span class="lineno">  750</span>&#160;</div><div class="line"><a name="l00751"></a><span class="lineno">  751</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00752"></a><span class="lineno">  752</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160;      result_ptr[i] = __hsub2(lhs_ptr[i], rhs_pair);</div><div class="line"><a name="l00754"></a><span class="lineno">  754</span>&#160;    }</div><div class="line"><a name="l00755"></a><span class="lineno">  755</span>&#160;</div><div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l00757"></a><span class="lineno">  757</span>&#160;      __half <span class="keyword">const</span> *a_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00758"></a><span class="lineno">  758</span>&#160;      __half d_residual = __hsub(a_residual_ptr[N - 1], reinterpret_cast&lt;__half const &amp;&gt;(rhs));</div><div class="line"><a name="l00759"></a><span class="lineno">  759</span>&#160;</div><div class="line"><a name="l00760"></a><span class="lineno">  760</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l00761"></a><span class="lineno">  761</span>&#160;    }</div><div class="line"><a name="l00762"></a><span class="lineno">  762</span>&#160;</div><div class="line"><a name="l00763"></a><span class="lineno">  763</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00764"></a><span class="lineno">  764</span>&#160;</div><div class="line"><a name="l00765"></a><span class="lineno">  765</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00766"></a><span class="lineno">  766</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00767"></a><span class="lineno">  767</span>&#160;      result[i] = lhs[i] - rhs;</div><div class="line"><a name="l00768"></a><span class="lineno">  768</span>&#160;    }</div><div class="line"><a name="l00769"></a><span class="lineno">  769</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00770"></a><span class="lineno">  770</span>&#160;</div><div class="line"><a name="l00771"></a><span class="lineno">  771</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00772"></a><span class="lineno">  772</span>&#160;  }</div><div class="line"><a name="l00773"></a><span class="lineno">  773</span>&#160;};</div><div class="line"><a name="l00774"></a><span class="lineno">  774</span>&#160;</div><div class="line"><a name="l00775"></a><span class="lineno">  775</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00776"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4.html">  776</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1multiplies.html">multiplies</a>&lt;Array&lt;<a class="code" href="structcutlass_1_1half__t.html">half_t</a>, N&gt;&gt; {</div><div class="line"><a name="l00777"></a><span class="lineno">  777</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00778"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a7de36b2383e45e0a4e7349e55321a887">  778</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a7de36b2383e45e0a4e7349e55321a887">operator()</a>(Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp; lhs, Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00779"></a><span class="lineno">  779</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00780"></a><span class="lineno">  780</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00781"></a><span class="lineno">  781</span>&#160;</div><div class="line"><a name="l00782"></a><span class="lineno">  782</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00783"></a><span class="lineno">  783</span>&#160;    __half2 <span class="keyword">const</span> *lhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00784"></a><span class="lineno">  784</span>&#160;    __half2 <span class="keyword">const</span> *rhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00785"></a><span class="lineno">  785</span>&#160;</div><div class="line"><a name="l00786"></a><span class="lineno">  786</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00787"></a><span class="lineno">  787</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l00788"></a><span class="lineno">  788</span>&#160;      result_ptr[i] = __hmul2(lhs_ptr[i], rhs_ptr[i]);</div><div class="line"><a name="l00789"></a><span class="lineno">  789</span>&#160;    }</div><div class="line"><a name="l00790"></a><span class="lineno">  790</span>&#160;</div><div class="line"><a name="l00791"></a><span class="lineno">  791</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l00792"></a><span class="lineno">  792</span>&#160;      __half <span class="keyword">const</span> *a_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00793"></a><span class="lineno">  793</span>&#160;      __half <span class="keyword">const</span> *b_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00794"></a><span class="lineno">  794</span>&#160;      __half d_residual = __hmul(a_residual_ptr[N - 1], b_residual_ptr[N - 1]);</div><div class="line"><a name="l00795"></a><span class="lineno">  795</span>&#160;</div><div class="line"><a name="l00796"></a><span class="lineno">  796</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l00797"></a><span class="lineno">  797</span>&#160;    }</div><div class="line"><a name="l00798"></a><span class="lineno">  798</span>&#160;</div><div class="line"><a name="l00799"></a><span class="lineno">  799</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00800"></a><span class="lineno">  800</span>&#160;</div><div class="line"><a name="l00801"></a><span class="lineno">  801</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00802"></a><span class="lineno">  802</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00803"></a><span class="lineno">  803</span>&#160;      result[i] = lhs[i] * rhs[i];</div><div class="line"><a name="l00804"></a><span class="lineno">  804</span>&#160;    }</div><div class="line"><a name="l00805"></a><span class="lineno">  805</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00806"></a><span class="lineno">  806</span>&#160;</div><div class="line"><a name="l00807"></a><span class="lineno">  807</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00808"></a><span class="lineno">  808</span>&#160;  }</div><div class="line"><a name="l00809"></a><span class="lineno">  809</span>&#160;</div><div class="line"><a name="l00810"></a><span class="lineno">  810</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00811"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4.html#ac4daebdadda5e8c5d6ca451967510856">  811</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4.html#ac4daebdadda5e8c5d6ca451967510856">operator()</a>(<a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const</span> &amp; lhs, Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00812"></a><span class="lineno">  812</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00813"></a><span class="lineno">  813</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00814"></a><span class="lineno">  814</span>&#160;</div><div class="line"><a name="l00815"></a><span class="lineno">  815</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00816"></a><span class="lineno">  816</span>&#160;    __half2 lhs_pair = __half2half2(reinterpret_cast&lt;__half const &amp;&gt;(lhs));</div><div class="line"><a name="l00817"></a><span class="lineno">  817</span>&#160;    __half2 <span class="keyword">const</span> *rhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00818"></a><span class="lineno">  818</span>&#160;</div><div class="line"><a name="l00819"></a><span class="lineno">  819</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00820"></a><span class="lineno">  820</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l00821"></a><span class="lineno">  821</span>&#160;      result_ptr[i] = __hmul2(lhs_pair, rhs_ptr[i]);</div><div class="line"><a name="l00822"></a><span class="lineno">  822</span>&#160;    }</div><div class="line"><a name="l00823"></a><span class="lineno">  823</span>&#160;</div><div class="line"><a name="l00824"></a><span class="lineno">  824</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l00825"></a><span class="lineno">  825</span>&#160;      __half <span class="keyword">const</span> *b_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00826"></a><span class="lineno">  826</span>&#160;</div><div class="line"><a name="l00827"></a><span class="lineno">  827</span>&#160;      __half d_residual = __hmul(</div><div class="line"><a name="l00828"></a><span class="lineno">  828</span>&#160;        reinterpret_cast&lt;__half const &amp;&gt;(lhs), </div><div class="line"><a name="l00829"></a><span class="lineno">  829</span>&#160;        b_residual_ptr[N - 1]);</div><div class="line"><a name="l00830"></a><span class="lineno">  830</span>&#160;</div><div class="line"><a name="l00831"></a><span class="lineno">  831</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l00832"></a><span class="lineno">  832</span>&#160;    }</div><div class="line"><a name="l00833"></a><span class="lineno">  833</span>&#160;</div><div class="line"><a name="l00834"></a><span class="lineno">  834</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00835"></a><span class="lineno">  835</span>&#160;</div><div class="line"><a name="l00836"></a><span class="lineno">  836</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00837"></a><span class="lineno">  837</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00838"></a><span class="lineno">  838</span>&#160;      result[i] = lhs * rhs[i];</div><div class="line"><a name="l00839"></a><span class="lineno">  839</span>&#160;    }</div><div class="line"><a name="l00840"></a><span class="lineno">  840</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00841"></a><span class="lineno">  841</span>&#160;</div><div class="line"><a name="l00842"></a><span class="lineno">  842</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00843"></a><span class="lineno">  843</span>&#160;  }</div><div class="line"><a name="l00844"></a><span class="lineno">  844</span>&#160;</div><div class="line"><a name="l00845"></a><span class="lineno">  845</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00846"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a1cb3ab75146da628432a46deef9babb5">  846</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a1cb3ab75146da628432a46deef9babb5">operator()</a>(Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp; lhs, <a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00847"></a><span class="lineno">  847</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00848"></a><span class="lineno">  848</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00849"></a><span class="lineno">  849</span>&#160;</div><div class="line"><a name="l00850"></a><span class="lineno">  850</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00851"></a><span class="lineno">  851</span>&#160;    __half2 <span class="keyword">const</span> *lhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00852"></a><span class="lineno">  852</span>&#160;    __half2 rhs_pair = __half2half2(reinterpret_cast&lt;__half const &amp;&gt;(rhs));</div><div class="line"><a name="l00853"></a><span class="lineno">  853</span>&#160;</div><div class="line"><a name="l00854"></a><span class="lineno">  854</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00855"></a><span class="lineno">  855</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l00856"></a><span class="lineno">  856</span>&#160;      result_ptr[i] = __hmul2(lhs_ptr[i], rhs_pair);</div><div class="line"><a name="l00857"></a><span class="lineno">  857</span>&#160;    }</div><div class="line"><a name="l00858"></a><span class="lineno">  858</span>&#160;</div><div class="line"><a name="l00859"></a><span class="lineno">  859</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l00860"></a><span class="lineno">  860</span>&#160;      __half <span class="keyword">const</span> *a_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00861"></a><span class="lineno">  861</span>&#160;</div><div class="line"><a name="l00862"></a><span class="lineno">  862</span>&#160;      __half d_residual = __hmul(</div><div class="line"><a name="l00863"></a><span class="lineno">  863</span>&#160;        a_residual_ptr[N - 1], </div><div class="line"><a name="l00864"></a><span class="lineno">  864</span>&#160;        reinterpret_cast&lt;__half const &amp;&gt;(rhs));</div><div class="line"><a name="l00865"></a><span class="lineno">  865</span>&#160;</div><div class="line"><a name="l00866"></a><span class="lineno">  866</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l00867"></a><span class="lineno">  867</span>&#160;    }</div><div class="line"><a name="l00868"></a><span class="lineno">  868</span>&#160;</div><div class="line"><a name="l00869"></a><span class="lineno">  869</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00870"></a><span class="lineno">  870</span>&#160;</div><div class="line"><a name="l00871"></a><span class="lineno">  871</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00872"></a><span class="lineno">  872</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00873"></a><span class="lineno">  873</span>&#160;      result[i] = lhs[i] * rhs;</div><div class="line"><a name="l00874"></a><span class="lineno">  874</span>&#160;    }</div><div class="line"><a name="l00875"></a><span class="lineno">  875</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00876"></a><span class="lineno">  876</span>&#160;</div><div class="line"><a name="l00877"></a><span class="lineno">  877</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00878"></a><span class="lineno">  878</span>&#160;  }</div><div class="line"><a name="l00879"></a><span class="lineno">  879</span>&#160;};</div><div class="line"><a name="l00880"></a><span class="lineno">  880</span>&#160;</div><div class="line"><a name="l00881"></a><span class="lineno">  881</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00882"></a><span class="lineno"><a class="line" href="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4.html">  882</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1divides.html">divides</a>&lt;Array&lt;<a class="code" href="structcutlass_1_1half__t.html">half_t</a>, N&gt;&gt; {</div><div class="line"><a name="l00883"></a><span class="lineno">  883</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00884"></a><span class="lineno"><a class="line" href="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a79459449652aa53d523b043e7098604c">  884</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a79459449652aa53d523b043e7098604c">operator()</a>(Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp; lhs, Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00885"></a><span class="lineno">  885</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00886"></a><span class="lineno">  886</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00887"></a><span class="lineno">  887</span>&#160;</div><div class="line"><a name="l00888"></a><span class="lineno">  888</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00889"></a><span class="lineno">  889</span>&#160;    __half2 <span class="keyword">const</span> *lhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00890"></a><span class="lineno">  890</span>&#160;    __half2 <span class="keyword">const</span> *rhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00891"></a><span class="lineno">  891</span>&#160;</div><div class="line"><a name="l00892"></a><span class="lineno">  892</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00893"></a><span class="lineno">  893</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l00894"></a><span class="lineno">  894</span>&#160;      result_ptr[i] = __h2div(lhs_ptr[i], rhs_ptr[i]);</div><div class="line"><a name="l00895"></a><span class="lineno">  895</span>&#160;    }</div><div class="line"><a name="l00896"></a><span class="lineno">  896</span>&#160;</div><div class="line"><a name="l00897"></a><span class="lineno">  897</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l00898"></a><span class="lineno">  898</span>&#160;      __half <span class="keyword">const</span> *a_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00899"></a><span class="lineno">  899</span>&#160;      __half <span class="keyword">const</span> *b_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00900"></a><span class="lineno">  900</span>&#160;</div><div class="line"><a name="l00901"></a><span class="lineno">  901</span>&#160;      __half d_residual = __hdiv(</div><div class="line"><a name="l00902"></a><span class="lineno">  902</span>&#160;        a_residual_ptr[N - 1], </div><div class="line"><a name="l00903"></a><span class="lineno">  903</span>&#160;        b_residual_ptr[N - 1]);</div><div class="line"><a name="l00904"></a><span class="lineno">  904</span>&#160;</div><div class="line"><a name="l00905"></a><span class="lineno">  905</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l00906"></a><span class="lineno">  906</span>&#160;    }</div><div class="line"><a name="l00907"></a><span class="lineno">  907</span>&#160;</div><div class="line"><a name="l00908"></a><span class="lineno">  908</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00909"></a><span class="lineno">  909</span>&#160;</div><div class="line"><a name="l00910"></a><span class="lineno">  910</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00911"></a><span class="lineno">  911</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00912"></a><span class="lineno">  912</span>&#160;      result[i] = lhs[i] / rhs[i];</div><div class="line"><a name="l00913"></a><span class="lineno">  913</span>&#160;    }</div><div class="line"><a name="l00914"></a><span class="lineno">  914</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00915"></a><span class="lineno">  915</span>&#160;</div><div class="line"><a name="l00916"></a><span class="lineno">  916</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00917"></a><span class="lineno">  917</span>&#160;  }</div><div class="line"><a name="l00918"></a><span class="lineno">  918</span>&#160;</div><div class="line"><a name="l00919"></a><span class="lineno">  919</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00920"></a><span class="lineno"><a class="line" href="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4.html#ad9a7972b8fb04ffb81fda91154dc4d7a">  920</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4.html#ad9a7972b8fb04ffb81fda91154dc4d7a">operator()</a>(<a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const</span> &amp; lhs, Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00921"></a><span class="lineno">  921</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00922"></a><span class="lineno">  922</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00923"></a><span class="lineno">  923</span>&#160;</div><div class="line"><a name="l00924"></a><span class="lineno">  924</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00925"></a><span class="lineno">  925</span>&#160;    __half2 lhs_pair = __half2half2(reinterpret_cast&lt;__half const &amp;&gt;(lhs));</div><div class="line"><a name="l00926"></a><span class="lineno">  926</span>&#160;    __half2 <span class="keyword">const</span> *rhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00927"></a><span class="lineno">  927</span>&#160;</div><div class="line"><a name="l00928"></a><span class="lineno">  928</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00929"></a><span class="lineno">  929</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l00930"></a><span class="lineno">  930</span>&#160;      result_ptr[i] = __h2div(lhs_pair, rhs_ptr[i]);</div><div class="line"><a name="l00931"></a><span class="lineno">  931</span>&#160;    }</div><div class="line"><a name="l00932"></a><span class="lineno">  932</span>&#160;</div><div class="line"><a name="l00933"></a><span class="lineno">  933</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l00934"></a><span class="lineno">  934</span>&#160;      __half <span class="keyword">const</span> *b_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;rhs);</div><div class="line"><a name="l00935"></a><span class="lineno">  935</span>&#160;</div><div class="line"><a name="l00936"></a><span class="lineno">  936</span>&#160;      __half d_residual = __hdiv(</div><div class="line"><a name="l00937"></a><span class="lineno">  937</span>&#160;        reinterpret_cast&lt;__half const &amp;&gt;(lhs), </div><div class="line"><a name="l00938"></a><span class="lineno">  938</span>&#160;        b_residual_ptr[N - 1]);</div><div class="line"><a name="l00939"></a><span class="lineno">  939</span>&#160;</div><div class="line"><a name="l00940"></a><span class="lineno">  940</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l00941"></a><span class="lineno">  941</span>&#160;    }</div><div class="line"><a name="l00942"></a><span class="lineno">  942</span>&#160;</div><div class="line"><a name="l00943"></a><span class="lineno">  943</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00944"></a><span class="lineno">  944</span>&#160;</div><div class="line"><a name="l00945"></a><span class="lineno">  945</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00946"></a><span class="lineno">  946</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00947"></a><span class="lineno">  947</span>&#160;      result[i] = lhs / rhs[i];</div><div class="line"><a name="l00948"></a><span class="lineno">  948</span>&#160;    }</div><div class="line"><a name="l00949"></a><span class="lineno">  949</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00950"></a><span class="lineno">  950</span>&#160;</div><div class="line"><a name="l00951"></a><span class="lineno">  951</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00952"></a><span class="lineno">  952</span>&#160;  }</div><div class="line"><a name="l00953"></a><span class="lineno">  953</span>&#160;</div><div class="line"><a name="l00954"></a><span class="lineno">  954</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00955"></a><span class="lineno"><a class="line" href="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a791580d56c3d73c18d3516d6bca52219">  955</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a791580d56c3d73c18d3516d6bca52219">operator()</a>(Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp; lhs, <a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const</span> &amp;rhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00956"></a><span class="lineno">  956</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00957"></a><span class="lineno">  957</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00958"></a><span class="lineno">  958</span>&#160;</div><div class="line"><a name="l00959"></a><span class="lineno">  959</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00960"></a><span class="lineno">  960</span>&#160;    __half2 <span class="keyword">const</span> *lhs_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00961"></a><span class="lineno">  961</span>&#160;    __half2 rhs_pair = __half2half2(reinterpret_cast&lt;__half const &amp;&gt;(rhs));</div><div class="line"><a name="l00962"></a><span class="lineno">  962</span>&#160;</div><div class="line"><a name="l00963"></a><span class="lineno">  963</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00964"></a><span class="lineno">  964</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l00965"></a><span class="lineno">  965</span>&#160;      result_ptr[i] = __h2div(lhs_ptr[i], rhs_pair);</div><div class="line"><a name="l00966"></a><span class="lineno">  966</span>&#160;    }</div><div class="line"><a name="l00967"></a><span class="lineno">  967</span>&#160;</div><div class="line"><a name="l00968"></a><span class="lineno">  968</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l00969"></a><span class="lineno">  969</span>&#160;      __half <span class="keyword">const</span> *a_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00970"></a><span class="lineno">  970</span>&#160;</div><div class="line"><a name="l00971"></a><span class="lineno">  971</span>&#160;      __half d_residual = __hdiv(</div><div class="line"><a name="l00972"></a><span class="lineno">  972</span>&#160;        a_residual_ptr[N - 1], </div><div class="line"><a name="l00973"></a><span class="lineno">  973</span>&#160;        reinterpret_cast&lt;__half const &amp;&gt;(rhs));</div><div class="line"><a name="l00974"></a><span class="lineno">  974</span>&#160;</div><div class="line"><a name="l00975"></a><span class="lineno">  975</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l00976"></a><span class="lineno">  976</span>&#160;    }</div><div class="line"><a name="l00977"></a><span class="lineno">  977</span>&#160;</div><div class="line"><a name="l00978"></a><span class="lineno">  978</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l00979"></a><span class="lineno">  979</span>&#160;</div><div class="line"><a name="l00980"></a><span class="lineno">  980</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00981"></a><span class="lineno">  981</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l00982"></a><span class="lineno">  982</span>&#160;      result[i] = lhs[i] / rhs;</div><div class="line"><a name="l00983"></a><span class="lineno">  983</span>&#160;    }</div><div class="line"><a name="l00984"></a><span class="lineno">  984</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l00985"></a><span class="lineno">  985</span>&#160;</div><div class="line"><a name="l00986"></a><span class="lineno">  986</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00987"></a><span class="lineno">  987</span>&#160;  }</div><div class="line"><a name="l00988"></a><span class="lineno">  988</span>&#160;};</div><div class="line"><a name="l00989"></a><span class="lineno">  989</span>&#160;</div><div class="line"><a name="l00990"></a><span class="lineno">  990</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00991"></a><span class="lineno"><a class="line" href="structcutlass_1_1negate_3_01Array_3_01half__t_00_01N_01_4_01_4.html">  991</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1negate.html">negate</a>&lt;Array&lt;<a class="code" href="structcutlass_1_1half__t.html">half_t</a>, N&gt;&gt; {</div><div class="line"><a name="l00992"></a><span class="lineno">  992</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00993"></a><span class="lineno"><a class="line" href="structcutlass_1_1negate_3_01Array_3_01half__t_00_01N_01_4_01_4.html#aecf43e7a3abad362be771be2b98c7f71">  993</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1negate_3_01Array_3_01half__t_00_01N_01_4_01_4.html#aecf43e7a3abad362be771be2b98c7f71">operator()</a>(Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp; lhs)<span class="keyword"> const </span>{</div><div class="line"><a name="l00994"></a><span class="lineno">  994</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l00995"></a><span class="lineno">  995</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l00996"></a><span class="lineno">  996</span>&#160;</div><div class="line"><a name="l00997"></a><span class="lineno">  997</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l00998"></a><span class="lineno">  998</span>&#160;    __half2 <span class="keyword">const</span> *source_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;lhs);</div><div class="line"><a name="l00999"></a><span class="lineno">  999</span>&#160;</div><div class="line"><a name="l01000"></a><span class="lineno"> 1000</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l01001"></a><span class="lineno"> 1001</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l01002"></a><span class="lineno"> 1002</span>&#160;      result_ptr[i] = __hneg2(source_ptr[i]);</div><div class="line"><a name="l01003"></a><span class="lineno"> 1003</span>&#160;    }</div><div class="line"><a name="l01004"></a><span class="lineno"> 1004</span>&#160;</div><div class="line"><a name="l01005"></a><span class="lineno"> 1005</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l01006"></a><span class="lineno"> 1006</span>&#160;      <a class="code" href="structcutlass_1_1half__t.html">half_t</a> x = lhs[N - 1];</div><div class="line"><a name="l01007"></a><span class="lineno"> 1007</span>&#160;      __half lhs_val = -<span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(x);</div><div class="line"><a name="l01008"></a><span class="lineno"> 1008</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(lhs_val);</div><div class="line"><a name="l01009"></a><span class="lineno"> 1009</span>&#160;    }</div><div class="line"><a name="l01010"></a><span class="lineno"> 1010</span>&#160;</div><div class="line"><a name="l01011"></a><span class="lineno"> 1011</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l01012"></a><span class="lineno"> 1012</span>&#160;</div><div class="line"><a name="l01013"></a><span class="lineno"> 1013</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l01014"></a><span class="lineno"> 1014</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l01015"></a><span class="lineno"> 1015</span>&#160;      result[i] = -lhs[i];</div><div class="line"><a name="l01016"></a><span class="lineno"> 1016</span>&#160;    }</div><div class="line"><a name="l01017"></a><span class="lineno"> 1017</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l01018"></a><span class="lineno"> 1018</span>&#160;</div><div class="line"><a name="l01019"></a><span class="lineno"> 1019</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l01020"></a><span class="lineno"> 1020</span>&#160;  }</div><div class="line"><a name="l01021"></a><span class="lineno"> 1021</span>&#160;};</div><div class="line"><a name="l01022"></a><span class="lineno"> 1022</span>&#160;</div><div class="line"><a name="l01024"></a><span class="lineno"> 1024</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l01025"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html"> 1025</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1multiply__add.html">multiply_add</a>&lt;Array&lt;<a class="code" href="structcutlass_1_1half__t.html">half_t</a>, N&gt;, Array&lt;<a class="code" href="structcutlass_1_1half__t.html">half_t</a>, N&gt;, Array&lt;<a class="code" href="structcutlass_1_1half__t.html">half_t</a>, N&gt;&gt; {</div><div class="line"><a name="l01026"></a><span class="lineno"> 1026</span>&#160;</div><div class="line"><a name="l01027"></a><span class="lineno"> 1027</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01028"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html#abc9dd51cad4f2997dae521fad8f5b486"> 1028</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html#abc9dd51cad4f2997dae521fad8f5b486">operator()</a>(</div><div class="line"><a name="l01029"></a><span class="lineno"> 1029</span>&#160;    Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;a, </div><div class="line"><a name="l01030"></a><span class="lineno"> 1030</span>&#160;    Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;b, </div><div class="line"><a name="l01031"></a><span class="lineno"> 1031</span>&#160;    Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;c)<span class="keyword"> const </span>{</div><div class="line"><a name="l01032"></a><span class="lineno"> 1032</span>&#160;    </div><div class="line"><a name="l01033"></a><span class="lineno"> 1033</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l01034"></a><span class="lineno"> 1034</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l01035"></a><span class="lineno"> 1035</span>&#160;</div><div class="line"><a name="l01036"></a><span class="lineno"> 1036</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l01037"></a><span class="lineno"> 1037</span>&#160;    __half2 <span class="keyword">const</span> *a_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;a);</div><div class="line"><a name="l01038"></a><span class="lineno"> 1038</span>&#160;    __half2 <span class="keyword">const</span> *b_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;b);</div><div class="line"><a name="l01039"></a><span class="lineno"> 1039</span>&#160;    __half2 <span class="keyword">const</span> *c_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;c);</div><div class="line"><a name="l01040"></a><span class="lineno"> 1040</span>&#160;</div><div class="line"><a name="l01041"></a><span class="lineno"> 1041</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l01042"></a><span class="lineno"> 1042</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l01043"></a><span class="lineno"> 1043</span>&#160;      result_ptr[i] = __hfma2(a_ptr[i], b_ptr[i], c_ptr[i]);</div><div class="line"><a name="l01044"></a><span class="lineno"> 1044</span>&#160;    }</div><div class="line"><a name="l01045"></a><span class="lineno"> 1045</span>&#160;</div><div class="line"><a name="l01046"></a><span class="lineno"> 1046</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l01047"></a><span class="lineno"> 1047</span>&#160;</div><div class="line"><a name="l01048"></a><span class="lineno"> 1048</span>&#160;      __half <span class="keyword">const</span> *a_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;a);</div><div class="line"><a name="l01049"></a><span class="lineno"> 1049</span>&#160;      __half <span class="keyword">const</span> *b_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;b);</div><div class="line"><a name="l01050"></a><span class="lineno"> 1050</span>&#160;      __half <span class="keyword">const</span> *c_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;c);</div><div class="line"><a name="l01051"></a><span class="lineno"> 1051</span>&#160;</div><div class="line"><a name="l01052"></a><span class="lineno"> 1052</span>&#160;      __half d_residual = __hfma(</div><div class="line"><a name="l01053"></a><span class="lineno"> 1053</span>&#160;        a_residual_ptr[N - 1], </div><div class="line"><a name="l01054"></a><span class="lineno"> 1054</span>&#160;        b_residual_ptr[N - 1], </div><div class="line"><a name="l01055"></a><span class="lineno"> 1055</span>&#160;        c_residual_ptr[N - 1]);</div><div class="line"><a name="l01056"></a><span class="lineno"> 1056</span>&#160;</div><div class="line"><a name="l01057"></a><span class="lineno"> 1057</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l01058"></a><span class="lineno"> 1058</span>&#160;    }</div><div class="line"><a name="l01059"></a><span class="lineno"> 1059</span>&#160;</div><div class="line"><a name="l01060"></a><span class="lineno"> 1060</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l01061"></a><span class="lineno"> 1061</span>&#160;</div><div class="line"><a name="l01062"></a><span class="lineno"> 1062</span>&#160;    <a class="code" href="structcutlass_1_1multiply__add.html">multiply_add&lt;half_t&gt;</a> op;</div><div class="line"><a name="l01063"></a><span class="lineno"> 1063</span>&#160;</div><div class="line"><a name="l01064"></a><span class="lineno"> 1064</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l01065"></a><span class="lineno"> 1065</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l01066"></a><span class="lineno"> 1066</span>&#160;      result[i] = op(a[i], b[i], c[i]);</div><div class="line"><a name="l01067"></a><span class="lineno"> 1067</span>&#160;    }</div><div class="line"><a name="l01068"></a><span class="lineno"> 1068</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l01069"></a><span class="lineno"> 1069</span>&#160;</div><div class="line"><a name="l01070"></a><span class="lineno"> 1070</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l01071"></a><span class="lineno"> 1071</span>&#160;  }</div><div class="line"><a name="l01072"></a><span class="lineno"> 1072</span>&#160;</div><div class="line"><a name="l01073"></a><span class="lineno"> 1073</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01074"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html#a7659a559754edb4949d54cc641a5bd01"> 1074</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html#a7659a559754edb4949d54cc641a5bd01">operator()</a>(</div><div class="line"><a name="l01075"></a><span class="lineno"> 1075</span>&#160;    <a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const</span> &amp;a, </div><div class="line"><a name="l01076"></a><span class="lineno"> 1076</span>&#160;    Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;b, </div><div class="line"><a name="l01077"></a><span class="lineno"> 1077</span>&#160;    Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;c)<span class="keyword"> const </span>{</div><div class="line"><a name="l01078"></a><span class="lineno"> 1078</span>&#160;    </div><div class="line"><a name="l01079"></a><span class="lineno"> 1079</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l01080"></a><span class="lineno"> 1080</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l01081"></a><span class="lineno"> 1081</span>&#160;</div><div class="line"><a name="l01082"></a><span class="lineno"> 1082</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l01083"></a><span class="lineno"> 1083</span>&#160;    __half2 a_pair = __half2half2(reinterpret_cast&lt;__half const &amp;&gt;(a));</div><div class="line"><a name="l01084"></a><span class="lineno"> 1084</span>&#160;    __half2 <span class="keyword">const</span> *b_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;b);</div><div class="line"><a name="l01085"></a><span class="lineno"> 1085</span>&#160;    __half2 <span class="keyword">const</span> *c_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;c);</div><div class="line"><a name="l01086"></a><span class="lineno"> 1086</span>&#160;</div><div class="line"><a name="l01087"></a><span class="lineno"> 1087</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l01088"></a><span class="lineno"> 1088</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l01089"></a><span class="lineno"> 1089</span>&#160;      result_ptr[i] = __hfma2(a_pair, b_ptr[i], c_ptr[i]);</div><div class="line"><a name="l01090"></a><span class="lineno"> 1090</span>&#160;    }</div><div class="line"><a name="l01091"></a><span class="lineno"> 1091</span>&#160;</div><div class="line"><a name="l01092"></a><span class="lineno"> 1092</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l01093"></a><span class="lineno"> 1093</span>&#160;</div><div class="line"><a name="l01094"></a><span class="lineno"> 1094</span>&#160;      __half <span class="keyword">const</span> *b_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;b);</div><div class="line"><a name="l01095"></a><span class="lineno"> 1095</span>&#160;      __half <span class="keyword">const</span> *c_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;c);</div><div class="line"><a name="l01096"></a><span class="lineno"> 1096</span>&#160;      __half d_residual = __hfma(</div><div class="line"><a name="l01097"></a><span class="lineno"> 1097</span>&#160;        reinterpret_cast&lt;__half const &amp;&gt;(a), </div><div class="line"><a name="l01098"></a><span class="lineno"> 1098</span>&#160;        b_residual_ptr[N - 1], </div><div class="line"><a name="l01099"></a><span class="lineno"> 1099</span>&#160;        c_residual_ptr[N - 1]);</div><div class="line"><a name="l01100"></a><span class="lineno"> 1100</span>&#160;</div><div class="line"><a name="l01101"></a><span class="lineno"> 1101</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l01102"></a><span class="lineno"> 1102</span>&#160;    }</div><div class="line"><a name="l01103"></a><span class="lineno"> 1103</span>&#160;</div><div class="line"><a name="l01104"></a><span class="lineno"> 1104</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l01105"></a><span class="lineno"> 1105</span>&#160;</div><div class="line"><a name="l01106"></a><span class="lineno"> 1106</span>&#160;    <a class="code" href="structcutlass_1_1multiply__add.html">multiply_add&lt;half_t&gt;</a> op;</div><div class="line"><a name="l01107"></a><span class="lineno"> 1107</span>&#160;</div><div class="line"><a name="l01108"></a><span class="lineno"> 1108</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l01109"></a><span class="lineno"> 1109</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l01110"></a><span class="lineno"> 1110</span>&#160;      result[i] = op(a, b[i], c[i]);</div><div class="line"><a name="l01111"></a><span class="lineno"> 1111</span>&#160;    }</div><div class="line"><a name="l01112"></a><span class="lineno"> 1112</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l01113"></a><span class="lineno"> 1113</span>&#160;</div><div class="line"><a name="l01114"></a><span class="lineno"> 1114</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l01115"></a><span class="lineno"> 1115</span>&#160;  }</div><div class="line"><a name="l01116"></a><span class="lineno"> 1116</span>&#160;</div><div class="line"><a name="l01117"></a><span class="lineno"> 1117</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01118"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html#aed1e0930836e1da4c53fcdfb683847a1"> 1118</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html#aed1e0930836e1da4c53fcdfb683847a1">operator()</a>(</div><div class="line"><a name="l01119"></a><span class="lineno"> 1119</span>&#160;    Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;a, </div><div class="line"><a name="l01120"></a><span class="lineno"> 1120</span>&#160;    <a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const</span> &amp;b, </div><div class="line"><a name="l01121"></a><span class="lineno"> 1121</span>&#160;    Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;c)<span class="keyword"> const </span>{</div><div class="line"><a name="l01122"></a><span class="lineno"> 1122</span>&#160;    </div><div class="line"><a name="l01123"></a><span class="lineno"> 1123</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l01124"></a><span class="lineno"> 1124</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l01125"></a><span class="lineno"> 1125</span>&#160;</div><div class="line"><a name="l01126"></a><span class="lineno"> 1126</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l01127"></a><span class="lineno"> 1127</span>&#160;    __half2 <span class="keyword">const</span> *a_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;a);</div><div class="line"><a name="l01128"></a><span class="lineno"> 1128</span>&#160;    __half2 b_pair = __half2half2(reinterpret_cast&lt;__half const &amp;&gt;(b));</div><div class="line"><a name="l01129"></a><span class="lineno"> 1129</span>&#160;    __half2 <span class="keyword">const</span> *c_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;c);</div><div class="line"><a name="l01130"></a><span class="lineno"> 1130</span>&#160;</div><div class="line"><a name="l01131"></a><span class="lineno"> 1131</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l01132"></a><span class="lineno"> 1132</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l01133"></a><span class="lineno"> 1133</span>&#160;      result_ptr[i] = __hfma2(a_ptr[i], b_pair, c_ptr[i]);</div><div class="line"><a name="l01134"></a><span class="lineno"> 1134</span>&#160;    }</div><div class="line"><a name="l01135"></a><span class="lineno"> 1135</span>&#160;</div><div class="line"><a name="l01136"></a><span class="lineno"> 1136</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l01137"></a><span class="lineno"> 1137</span>&#160;</div><div class="line"><a name="l01138"></a><span class="lineno"> 1138</span>&#160;      __half <span class="keyword">const</span> *a_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;a);</div><div class="line"><a name="l01139"></a><span class="lineno"> 1139</span>&#160;      __half <span class="keyword">const</span> *c_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;c);</div><div class="line"><a name="l01140"></a><span class="lineno"> 1140</span>&#160;</div><div class="line"><a name="l01141"></a><span class="lineno"> 1141</span>&#160;      __half d_residual = __hfma(</div><div class="line"><a name="l01142"></a><span class="lineno"> 1142</span>&#160;        a_residual_ptr[N - 1], </div><div class="line"><a name="l01143"></a><span class="lineno"> 1143</span>&#160;        reinterpret_cast&lt;__half const &amp;&gt;(b), </div><div class="line"><a name="l01144"></a><span class="lineno"> 1144</span>&#160;        c_residual_ptr[N - 1]);</div><div class="line"><a name="l01145"></a><span class="lineno"> 1145</span>&#160;</div><div class="line"><a name="l01146"></a><span class="lineno"> 1146</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l01147"></a><span class="lineno"> 1147</span>&#160;    }</div><div class="line"><a name="l01148"></a><span class="lineno"> 1148</span>&#160;</div><div class="line"><a name="l01149"></a><span class="lineno"> 1149</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l01150"></a><span class="lineno"> 1150</span>&#160;</div><div class="line"><a name="l01151"></a><span class="lineno"> 1151</span>&#160;    <a class="code" href="structcutlass_1_1multiply__add.html">multiply_add&lt;half_t&gt;</a> op;</div><div class="line"><a name="l01152"></a><span class="lineno"> 1152</span>&#160;</div><div class="line"><a name="l01153"></a><span class="lineno"> 1153</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l01154"></a><span class="lineno"> 1154</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l01155"></a><span class="lineno"> 1155</span>&#160;      result[i] = op(a[i], b, c[i]);</div><div class="line"><a name="l01156"></a><span class="lineno"> 1156</span>&#160;    }</div><div class="line"><a name="l01157"></a><span class="lineno"> 1157</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l01158"></a><span class="lineno"> 1158</span>&#160;</div><div class="line"><a name="l01159"></a><span class="lineno"> 1159</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l01160"></a><span class="lineno"> 1160</span>&#160;  }</div><div class="line"><a name="l01161"></a><span class="lineno"> 1161</span>&#160;</div><div class="line"><a name="l01162"></a><span class="lineno"> 1162</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l01163"></a><span class="lineno"><a class="line" href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html#a6b4fe9d3366d389034a53f5ba71bdaee"> 1163</a></span>&#160;  Array&lt;half_t, N&gt; <a class="code" href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html#a6b4fe9d3366d389034a53f5ba71bdaee">operator()</a>(</div><div class="line"><a name="l01164"></a><span class="lineno"> 1164</span>&#160;    Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;a, </div><div class="line"><a name="l01165"></a><span class="lineno"> 1165</span>&#160;    Array&lt;half_t, N&gt; <span class="keyword">const</span> &amp;b, </div><div class="line"><a name="l01166"></a><span class="lineno"> 1166</span>&#160;    <a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const</span> &amp;c)<span class="keyword"> const </span>{</div><div class="line"><a name="l01167"></a><span class="lineno"> 1167</span>&#160;    </div><div class="line"><a name="l01168"></a><span class="lineno"> 1168</span>&#160;    Array&lt;half_t, N&gt; result;</div><div class="line"><a name="l01169"></a><span class="lineno"> 1169</span>&#160;<span class="preprocessor">    #if defined(__CUDA_ARCH__) &amp;&amp; (__CUDA_ARCH__ &gt;= 530)</span></div><div class="line"><a name="l01170"></a><span class="lineno"> 1170</span>&#160;</div><div class="line"><a name="l01171"></a><span class="lineno"> 1171</span>&#160;    __half2 *result_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 *<span class="keyword">&gt;</span>(&amp;result);</div><div class="line"><a name="l01172"></a><span class="lineno"> 1172</span>&#160;    __half2 <span class="keyword">const</span> *a_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;a);</div><div class="line"><a name="l01173"></a><span class="lineno"> 1173</span>&#160;    __half2 <span class="keyword">const</span> *b_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half2 <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;b);</div><div class="line"><a name="l01174"></a><span class="lineno"> 1174</span>&#160;    __half2 c_pair = __half2half2(reinterpret_cast&lt;__half const &amp;&gt;(c));</div><div class="line"><a name="l01175"></a><span class="lineno"> 1175</span>&#160;</div><div class="line"><a name="l01176"></a><span class="lineno"> 1176</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l01177"></a><span class="lineno"> 1177</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N / 2; ++i) {</div><div class="line"><a name="l01178"></a><span class="lineno"> 1178</span>&#160;      result_ptr[i] = __hfma2(a_ptr[i], b_ptr[i], c_pair);</div><div class="line"><a name="l01179"></a><span class="lineno"> 1179</span>&#160;    }</div><div class="line"><a name="l01180"></a><span class="lineno"> 1180</span>&#160;</div><div class="line"><a name="l01181"></a><span class="lineno"> 1181</span>&#160;    <span class="keywordflow">if</span> (N % 2) {</div><div class="line"><a name="l01182"></a><span class="lineno"> 1182</span>&#160;</div><div class="line"><a name="l01183"></a><span class="lineno"> 1183</span>&#160;      __half <span class="keyword">const</span> *a_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;a);</div><div class="line"><a name="l01184"></a><span class="lineno"> 1184</span>&#160;      __half <span class="keyword">const</span> *b_residual_ptr = <span class="keyword">reinterpret_cast&lt;</span>__half <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;b);</div><div class="line"><a name="l01185"></a><span class="lineno"> 1185</span>&#160;</div><div class="line"><a name="l01186"></a><span class="lineno"> 1186</span>&#160;      __half d_residual = __hfma(</div><div class="line"><a name="l01187"></a><span class="lineno"> 1187</span>&#160;        a_residual_ptr[N - 1], </div><div class="line"><a name="l01188"></a><span class="lineno"> 1188</span>&#160;        b_residual_ptr[N - 1], </div><div class="line"><a name="l01189"></a><span class="lineno"> 1189</span>&#160;        reinterpret_cast&lt;__half const &amp;&gt;(c));</div><div class="line"><a name="l01190"></a><span class="lineno"> 1190</span>&#160;</div><div class="line"><a name="l01191"></a><span class="lineno"> 1191</span>&#160;      result[N - 1] = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="structcutlass_1_1half__t.html">half_t</a> <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(d_residual);</div><div class="line"><a name="l01192"></a><span class="lineno"> 1192</span>&#160;    }</div><div class="line"><a name="l01193"></a><span class="lineno"> 1193</span>&#160;</div><div class="line"><a name="l01194"></a><span class="lineno"> 1194</span>&#160;<span class="preprocessor">    #else</span></div><div class="line"><a name="l01195"></a><span class="lineno"> 1195</span>&#160;</div><div class="line"><a name="l01196"></a><span class="lineno"> 1196</span>&#160;    <a class="code" href="structcutlass_1_1multiply__add.html">multiply_add&lt;half_t&gt;</a> op;</div><div class="line"><a name="l01197"></a><span class="lineno"> 1197</span>&#160;</div><div class="line"><a name="l01198"></a><span class="lineno"> 1198</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l01199"></a><span class="lineno"> 1199</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; N; ++i) {</div><div class="line"><a name="l01200"></a><span class="lineno"> 1200</span>&#160;      result[i] = op(a[i], b[i], c);</div><div class="line"><a name="l01201"></a><span class="lineno"> 1201</span>&#160;    }</div><div class="line"><a name="l01202"></a><span class="lineno"> 1202</span>&#160;<span class="preprocessor">    #endif</span></div><div class="line"><a name="l01203"></a><span class="lineno"> 1203</span>&#160;</div><div class="line"><a name="l01204"></a><span class="lineno"> 1204</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l01205"></a><span class="lineno"> 1205</span>&#160;  }</div><div class="line"><a name="l01206"></a><span class="lineno"> 1206</span>&#160;};</div><div class="line"><a name="l01207"></a><span class="lineno"> 1207</span>&#160;</div><div class="line"><a name="l01209"></a><span class="lineno"> 1209</span>&#160;</div><div class="line"><a name="l01210"></a><span class="lineno"> 1210</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="structcutlass_1_1multiply__add_html"><div class="ttname"><a href="structcutlass_1_1multiply__add.html">cutlass::multiply_add</a></div><div class="ttdoc">Fused multiply-add. </div><div class="ttdef"><b>Definition:</b> functional.h:92</div></div>
<div class="ttc" id="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4_html_a5dbfcd8f0f61adacea1643f95c57d9e7"><div class="ttname"><a href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html#a5dbfcd8f0f61adacea1643f95c57d9e7">cutlass::minimum&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(T const &amp;scalar, Array&lt; T, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:351</div></div>
<div class="ttc" id="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4_html_a7eb77069757fffa01b7fba384c1e4bd2"><div class="ttname"><a href="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a7eb77069757fffa01b7fba384c1e4bd2">cutlass::plus&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(Array&lt; half_t, N &gt; const &amp;lhs, Array&lt; half_t, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:578</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1xor__add_html_a2bda9d781aee02f9aa90c75071a73f98"><div class="ttname"><a href="structcutlass_1_1xor__add.html#a2bda9d781aee02f9aa90c75071a73f98">cutlass::xor_add::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T operator()(T const &amp;a, T const &amp;b, T const &amp;c) const </div><div class="ttdef"><b>Definition:</b> functional.h:103</div></div>
<div class="ttc" id="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4_html_a791580d56c3d73c18d3516d6bca52219"><div class="ttname"><a href="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a791580d56c3d73c18d3516d6bca52219">cutlass::divides&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(Array&lt; half_t, N &gt; const &amp;lhs, half_t const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:955</div></div>
<div class="ttc" id="complex_8h_html"><div class="ttname"><a href="complex_8h.html">complex.h</a></div></div>
<div class="ttc" id="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4_html_af7f0dbf65e39a1780a48472d168fa5c0"><div class="ttname"><a href="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4.html#af7f0dbf65e39a1780a48472d168fa5c0">cutlass::minus&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs, T const &amp;scalar) const </div><div class="ttdef"><b>Definition:</b> functional.h:383</div></div>
<div class="ttc" id="namespacecutlass_html_a236d41e43fc97943fb2412fcbb40aec1"><div class="ttname"><a href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">cutlass::imag</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE float const &amp; imag(cuFloatComplex const &amp;z)</div><div class="ttdoc">Returns the imaginary part of the complex number. </div><div class="ttdef"><b>Definition:</b> complex.h:72</div></div>
<div class="ttc" id="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4_html_a76637faf0a196f09e6e70318ac574a4b"><div class="ttname"><a href="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a76637faf0a196f09e6e70318ac574a4b">cutlass::minus&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(Array&lt; half_t, N &gt; const &amp;lhs, Array&lt; half_t, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:678</div></div>
<div class="ttc" id="structcutlass_1_1plus_html_a95661210dd2176c4b8ab86ffc3d5eb50"><div class="ttname"><a href="structcutlass_1_1plus.html#a95661210dd2176c4b8ab86ffc3d5eb50">cutlass::plus::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T operator()(T lhs, T const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:48</div></div>
<div class="ttc" id="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4_html_a8ae39f34fbeac5fc367503206b7074b0"><div class="ttname"><a href="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4.html#a8ae39f34fbeac5fc367503206b7074b0">cutlass::maximum&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs, T const &amp;scalar) const </div><div class="ttdef"><b>Definition:</b> functional.h:269</div></div>
<div class="ttc" id="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4_html_ac2b180299c6c3aa0eead2192c68b71e2"><div class="ttname"><a href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html#ac2b180299c6c3aa0eead2192c68b71e2">cutlass::minimum&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs, Array&lt; T, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:323</div></div>
<div class="ttc" id="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4_html_a2f1a13f5e00fc7756597dc8861fb9fc4"><div class="ttname"><a href="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a2f1a13f5e00fc7756597dc8861fb9fc4">cutlass::plus&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(half_t const &amp;lhs, Array&lt; half_t, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:611</div></div>
<div class="ttc" id="half_8h_html"><div class="ttname"><a href="half_8h.html">half.h</a></div><div class="ttdoc">Defines a class for using IEEE half-precision floating-point types in host or device code...</div></div>
<div class="ttc" id="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4_html_ad9a7972b8fb04ffb81fda91154dc4d7a"><div class="ttname"><a href="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4.html#ad9a7972b8fb04ffb81fda91154dc4d7a">cutlass::divides&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(half_t const &amp;lhs, Array&lt; half_t, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:920</div></div>
<div class="ttc" id="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4_html_a7de36b2383e45e0a4e7349e55321a887"><div class="ttname"><a href="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a7de36b2383e45e0a4e7349e55321a887">cutlass::multiplies&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(Array&lt; half_t, N &gt; const &amp;lhs, Array&lt; half_t, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:778</div></div>
<div class="ttc" id="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4_html_a93dda0b911aabecf4a115ec7692ff5bd"><div class="ttname"><a href="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4.html#a93dda0b911aabecf4a115ec7692ff5bd">cutlass::plus&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs, Array&lt; T, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:191</div></div>
<div class="ttc" id="structcutlass_1_1minimum_html"><div class="ttname"><a href="structcutlass_1_1minimum.html">cutlass::minimum</a></div><div class="ttdef"><b>Definition:</b> functional.h:298</div></div>
<div class="ttc" id="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4_html_af02b086d2775d769b9675de752a5351c"><div class="ttname"><a href="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4.html#af02b086d2775d769b9675de752a5351c">cutlass::maximum&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs, Array&lt; T, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:255</div></div>
<div class="ttc" id="structcutlass_1_1maximum_html"><div class="ttname"><a href="structcutlass_1_1maximum.html">cutlass::maximum</a></div><div class="ttdef"><b>Definition:</b> functional.h:235</div></div>
<div class="ttc" id="structcutlass_1_1half__t_html"><div class="ttname"><a href="structcutlass_1_1half__t.html">cutlass::half_t</a></div><div class="ttdoc">IEEE half-precision floating-point type. </div><div class="ttdef"><b>Definition:</b> half.h:126</div></div>
<div class="ttc" id="structcutlass_1_1negate_html_a63ff0d5bd31a54bcee8e1512182c8253"><div class="ttname"><a href="structcutlass_1_1negate.html#a63ff0d5bd31a54bcee8e1512182c8253">cutlass::negate::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T operator()(T lhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:85</div></div>
<div class="ttc" id="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4_html_ac4daebdadda5e8c5d6ca451967510856"><div class="ttname"><a href="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4.html#ac4daebdadda5e8c5d6ca451967510856">cutlass::multiplies&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(half_t const &amp;lhs, Array&lt; half_t, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:811</div></div>
<div class="ttc" id="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4_html_a78e99733c048338faac559ce6e33f643"><div class="ttname"><a href="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4.html#a78e99733c048338faac559ce6e33f643">cutlass::plus&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs, T const &amp;scalar) const </div><div class="ttdef"><b>Definition:</b> functional.h:205</div></div>
<div class="ttc" id="namespacecutlass_html_ac0ea92c9a2a594446a84f7f86a79e143"><div class="ttname"><a href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">cutlass::real</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE float const &amp; real(cuFloatComplex const &amp;z)</div><div class="ttdoc">Returns the real part of the complex number. </div><div class="ttdef"><b>Definition:</b> complex.h:56</div></div>
<div class="ttc" id="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4_html_a77e216f4b4c22699604ab06d9175bfd1"><div class="ttname"><a href="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a77e216f4b4c22699604ab06d9175bfd1">cutlass::minus&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(Array&lt; half_t, N &gt; const &amp;lhs, half_t const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:743</div></div>
<div class="ttc" id="structcutlass_1_1minimum_3_01float_01_4_html_a13bac6699851e89e63b7b852983d2212"><div class="ttname"><a href="structcutlass_1_1minimum_3_01float_01_4.html#a13bac6699851e89e63b7b852983d2212">cutlass::minimum&lt; float &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE float operator()(float const &amp;lhs, float const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:309</div></div>
<div class="ttc" id="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4_html_a4a45f7d2dad800165887d6bec93b16f1"><div class="ttname"><a href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html#a4a45f7d2dad800165887d6bec93b16f1">cutlass::minimum&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs, T const &amp;scalar) const </div><div class="ttdef"><b>Definition:</b> functional.h:337</div></div>
<div class="ttc" id="classcutlass_1_1complex_html_aa2c8964e10eeaa99b2d1a18cdcbd0105"><div class="ttname"><a href="classcutlass_1_1complex.html#aa2c8964e10eeaa99b2d1a18cdcbd0105">cutlass::complex::imag</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T const &amp; imag() const </div><div class="ttdoc">Accesses the imaginary part of the complex number. </div><div class="ttdef"><b>Definition:</b> complex.h:240</div></div>
<div class="ttc" id="structcutlass_1_1multiply__add_html_a842a468a8d77bda4b789fb9248926655"><div class="ttname"><a href="structcutlass_1_1multiply__add.html#a842a468a8d77bda4b789fb9248926655">cutlass::multiply_add::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE C operator()(A const &amp;a, B const &amp;b, C const &amp;c) const </div><div class="ttdef"><b>Definition:</b> functional.h:94</div></div>
<div class="ttc" id="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963_html_a6b4fe9d3366d389034a53f5ba71bdaee"><div class="ttname"><a href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html#a6b4fe9d3366d389034a53f5ba71bdaee">cutlass::multiply_add&lt; Array&lt; half_t, N &gt;, Array&lt; half_t, N &gt;, Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(Array&lt; half_t, N &gt; const &amp;a, Array&lt; half_t, N &gt; const &amp;b, half_t const &amp;c) const </div><div class="ttdef"><b>Definition:</b> functional.h:1163</div></div>
<div class="ttc" id="structcutlass_1_1plus_html"><div class="ttname"><a href="structcutlass_1_1plus.html">cutlass::plus</a></div><div class="ttdef"><b>Definition:</b> functional.h:46</div></div>
<div class="ttc" id="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4_html_ac11e165f6f34a5bc47cdecf6b85db1ab"><div class="ttname"><a href="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4.html#ac11e165f6f34a5bc47cdecf6b85db1ab">cutlass::minus&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs, Array&lt; T, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:369</div></div>
<div class="ttc" id="structcutlass_1_1maximum_html_a5425d2fc8a8028241332554378e980d7"><div class="ttname"><a href="structcutlass_1_1maximum.html#a5425d2fc8a8028241332554378e980d7">cutlass::maximum::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T operator()(T const &amp;lhs, T const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:238</div></div>
<div class="ttc" id="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4_html_a85beeba0468104af0df3c04bebe690d3"><div class="ttname"><a href="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4.html#a85beeba0468104af0df3c04bebe690d3">cutlass::multiplies&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs, Array&lt; T, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:415</div></div>
<div class="ttc" id="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4_html_a10e1dad45a537e3b92ebd5ce6561069a"><div class="ttname"><a href="structcutlass_1_1minus_3_01Array_3_01T_00_01N_01_4_01_4.html#a10e1dad45a537e3b92ebd5ce6561069a">cutlass::minus&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(T const &amp;scalar, Array&lt; T, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:397</div></div>
<div class="ttc" id="structcutlass_1_1multiplies_html_adff981528415c4c40b251a3401e642ad"><div class="ttname"><a href="structcutlass_1_1multiplies.html#adff981528415c4c40b251a3401e642ad">cutlass::multiplies::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T operator()(T lhs, T const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:66</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="structcutlass_1_1maximum_3_01float_01_4_html_adf9daaeef5c378bb6036c62c3eed9b3e"><div class="ttname"><a href="structcutlass_1_1maximum_3_01float_01_4.html#adf9daaeef5c378bb6036c62c3eed9b3e">cutlass::maximum&lt; float &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE float operator()(float const &amp;lhs, float const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:246</div></div>
<div class="ttc" id="structcutlass_1_1negate_html"><div class="ttname"><a href="structcutlass_1_1negate.html">cutlass::negate</a></div><div class="ttdef"><b>Definition:</b> functional.h:83</div></div>
<div class="ttc" id="structcutlass_1_1divides_html_a45c044899c466720712fa3a55dab57f1"><div class="ttname"><a href="structcutlass_1_1divides.html#a45c044899c466720712fa3a55dab57f1">cutlass::divides::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T operator()(T lhs, T const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:75</div></div>
<div class="ttc" id="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4_html_a2c69bb9e0339d9e500dfd0a5307e1dd6"><div class="ttname"><a href="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4.html#a2c69bb9e0339d9e500dfd0a5307e1dd6">cutlass::multiply_add&lt; Array&lt; T, N &gt;, Array&lt; T, N &gt;, Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;a, T const &amp;scalar, Array&lt; T, N &gt; const &amp;c) const </div><div class="ttdef"><b>Definition:</b> functional.h:541</div></div>
<div class="ttc" id="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4_html_a070cba31672b435b1edf16e6ded8c955"><div class="ttname"><a href="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4.html#a070cba31672b435b1edf16e6ded8c955">cutlass::multiply_add&lt; Array&lt; T, N &gt;, Array&lt; T, N &gt;, Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;a, Array&lt; T, N &gt; const &amp;b, Array&lt; T, N &gt; const &amp;c) const </div><div class="ttdef"><b>Definition:</b> functional.h:527</div></div>
<div class="ttc" id="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4_html_ad911e958e6fa2a43ecb8c0b004f2d7ab"><div class="ttname"><a href="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4.html#ad911e958e6fa2a43ecb8c0b004f2d7ab">cutlass::multiplies&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(T const &amp;scalar, Array&lt; T, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:443</div></div>
<div class="ttc" id="structcutlass_1_1multiplies_html"><div class="ttname"><a href="structcutlass_1_1multiplies.html">cutlass::multiplies</a></div><div class="ttdef"><b>Definition:</b> functional.h:64</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4_html_ae13cbdb4ed12e380cf4a858710a7d63f"><div class="ttname"><a href="structcutlass_1_1multiplies_3_01Array_3_01T_00_01N_01_4_01_4.html#ae13cbdb4ed12e380cf4a858710a7d63f">cutlass::multiplies&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs, T const &amp;scalar) const </div><div class="ttdef"><b>Definition:</b> functional.h:429</div></div>
<div class="ttc" id="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4_html_af0c3eaffe841ec2a2a8e93f10dafed1a"><div class="ttname"><a href="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4.html#af0c3eaffe841ec2a2a8e93f10dafed1a">cutlass::divides&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(T const &amp;scalar, Array&lt; T, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:489</div></div>
<div class="ttc" id="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4_html_a79459449652aa53d523b043e7098604c"><div class="ttname"><a href="structcutlass_1_1divides_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a79459449652aa53d523b043e7098604c">cutlass::divides&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(Array&lt; half_t, N &gt; const &amp;lhs, Array&lt; half_t, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:884</div></div>
<div class="ttc" id="structcutlass_1_1divides_html"><div class="ttname"><a href="structcutlass_1_1divides.html">cutlass::divides</a></div><div class="ttdef"><b>Definition:</b> functional.h:73</div></div>
<div class="ttc" id="classcutlass_1_1complex_html_afaeeace8c2987047400b83973e196aeb"><div class="ttname"><a href="classcutlass_1_1complex.html#afaeeace8c2987047400b83973e196aeb">cutlass::complex::real</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T const &amp; real() const </div><div class="ttdoc">Accesses the real part of the complex number. </div><div class="ttdef"><b>Definition:</b> complex.h:232</div></div>
<div class="ttc" id="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4_html_a0af810657ec58a868d05157e2c819f1f"><div class="ttname"><a href="structcutlass_1_1plus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a0af810657ec58a868d05157e2c819f1f">cutlass::plus&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(Array&lt; half_t, N &gt; const &amp;lhs, half_t const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:643</div></div>
<div class="ttc" id="classcutlass_1_1complex_html"><div class="ttname"><a href="classcutlass_1_1complex.html">cutlass::complex</a></div><div class="ttdef"><b>Definition:</b> complex.h:92</div></div>
<div class="ttc" id="structcutlass_1_1minimum_html_a15eb1ef96e29e5230ee59e89beadfa6b"><div class="ttname"><a href="structcutlass_1_1minimum.html#a15eb1ef96e29e5230ee59e89beadfa6b">cutlass::minimum::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T operator()(T const &amp;lhs, T const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:301</div></div>
<div class="ttc" id="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4_html_a4b42227184cb7c796460062c46a84b57"><div class="ttname"><a href="structcutlass_1_1minimum_3_01Array_3_01T_00_01N_01_4_01_4.html#a4b42227184cb7c796460062c46a84b57">cutlass::minimum&lt; Array&lt; T, N &gt; &gt;::scalar_op</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE T scalar_op(T const &amp;lhs, T const &amp;rhs)</div><div class="ttdef"><b>Definition:</b> functional.h:318</div></div>
<div class="ttc" id="structcutlass_1_1multiply__add_3_01T_00_01complex_3_01T_01_4_00_01complex_3_01T_01_4_01_4_html_a24b5049dfd834f3a84537b3fceba4e65"><div class="ttname"><a href="structcutlass_1_1multiply__add_3_01T_00_01complex_3_01T_01_4_00_01complex_3_01T_01_4_01_4.html#a24b5049dfd834f3a84537b3fceba4e65">cutlass::multiply_add&lt; T, complex&lt; T &gt;, complex&lt; T &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE complex&lt; T &gt; operator()(T const &amp;a, complex&lt; T &gt; const &amp;b, complex&lt; T &gt; const &amp;c) const </div><div class="ttdef"><b>Definition:</b> functional.h:164</div></div>
<div class="ttc" id="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963_html_a7659a559754edb4949d54cc641a5bd01"><div class="ttname"><a href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html#a7659a559754edb4949d54cc641a5bd01">cutlass::multiply_add&lt; Array&lt; half_t, N &gt;, Array&lt; half_t, N &gt;, Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(half_t const &amp;a, Array&lt; half_t, N &gt; const &amp;b, Array&lt; half_t, N &gt; const &amp;c) const </div><div class="ttdef"><b>Definition:</b> functional.h:1074</div></div>
<div class="ttc" id="structcutlass_1_1multiply__add_3_01complex_3_01T_01_4_00_01T_00_01complex_3_01T_01_4_01_4_html_a28aeeb385a3a47fb72a3d6f421c35294"><div class="ttname"><a href="structcutlass_1_1multiply__add_3_01complex_3_01T_01_4_00_01T_00_01complex_3_01T_01_4_01_4.html#a28aeeb385a3a47fb72a3d6f421c35294">cutlass::multiply_add&lt; complex&lt; T &gt;, T, complex&lt; T &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE complex&lt; T &gt; operator()(complex&lt; T &gt; const &amp;a, T const &amp;b, complex&lt; T &gt; const &amp;c) const </div><div class="ttdef"><b>Definition:</b> functional.h:142</div></div>
<div class="ttc" id="structcutlass_1_1negate_3_01Array_3_01T_00_01N_01_4_01_4_html_a96bbedf72574b7657c21661ad866639e"><div class="ttname"><a href="structcutlass_1_1negate_3_01Array_3_01T_00_01N_01_4_01_4.html#a96bbedf72574b7657c21661ad866639e">cutlass::negate&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:508</div></div>
<div class="ttc" id="structcutlass_1_1xor__add_html"><div class="ttname"><a href="structcutlass_1_1xor__add.html">cutlass::xor_add</a></div><div class="ttdoc">Fused multiply-add. </div><div class="ttdef"><b>Definition:</b> functional.h:101</div></div>
<div class="ttc" id="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4_html_a1949655a2c6aa18369f868060651fbd4"><div class="ttname"><a href="structcutlass_1_1maximum_3_01Array_3_01T_00_01N_01_4_01_4.html#a1949655a2c6aa18369f868060651fbd4">cutlass::maximum&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(T const &amp;scalar, Array&lt; T, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:283</div></div>
<div class="ttc" id="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4_html_ac21715e6d4b6cfbdbdfb8cb640694933"><div class="ttname"><a href="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4.html#ac21715e6d4b6cfbdbdfb8cb640694933">cutlass::divides&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs, T const &amp;scalar) const </div><div class="ttdef"><b>Definition:</b> functional.h:475</div></div>
<div class="ttc" id="structcutlass_1_1minus_html_aa0db9bb7741aa930c9a52e795d12f227"><div class="ttname"><a href="structcutlass_1_1minus.html#aa0db9bb7741aa930c9a52e795d12f227">cutlass::minus::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE T operator()(T lhs, T const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:57</div></div>
<div class="ttc" id="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963_html_aed1e0930836e1da4c53fcdfb683847a1"><div class="ttname"><a href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html#aed1e0930836e1da4c53fcdfb683847a1">cutlass::multiply_add&lt; Array&lt; half_t, N &gt;, Array&lt; half_t, N &gt;, Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(Array&lt; half_t, N &gt; const &amp;a, half_t const &amp;b, Array&lt; half_t, N &gt; const &amp;c) const </div><div class="ttdef"><b>Definition:</b> functional.h:1118</div></div>
<div class="ttc" id="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4_html_a94e077e7e186dad0c4ccb8d12bf47ffc"><div class="ttname"><a href="structcutlass_1_1plus_3_01Array_3_01T_00_01N_01_4_01_4.html#a94e077e7e186dad0c4ccb8d12bf47ffc">cutlass::plus&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(T const &amp;scalar, Array&lt; T, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:219</div></div>
<div class="ttc" id="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4_html_a177841dc0eb55619ce5c252905c366fd"><div class="ttname"><a href="structcutlass_1_1minus_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a177841dc0eb55619ce5c252905c366fd">cutlass::minus&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(half_t const &amp;lhs, Array&lt; half_t, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:711</div></div>
<div class="ttc" id="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4_html_a71a7c4fbfd3db644fb3cb2d995820856"><div class="ttname"><a href="structcutlass_1_1multiply__add_3_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_00_01Array_3_01T_00_01N_01_4_01_4.html#a71a7c4fbfd3db644fb3cb2d995820856">cutlass::multiply_add&lt; Array&lt; T, N &gt;, Array&lt; T, N &gt;, Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(T const &amp;scalar, Array&lt; T, N &gt; const &amp;b, Array&lt; T, N &gt; const &amp;c) const </div><div class="ttdef"><b>Definition:</b> functional.h:555</div></div>
<div class="ttc" id="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4_html_abe16d29dc2547723268d6a0a204550f4"><div class="ttname"><a href="structcutlass_1_1divides_3_01Array_3_01T_00_01N_01_4_01_4.html#abe16d29dc2547723268d6a0a204550f4">cutlass::divides&lt; Array&lt; T, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; T, N &gt; operator()(Array&lt; T, N &gt; const &amp;lhs, Array&lt; T, N &gt; const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:461</div></div>
<div class="ttc" id="structcutlass_1_1negate_3_01Array_3_01half__t_00_01N_01_4_01_4_html_aecf43e7a3abad362be771be2b98c7f71"><div class="ttname"><a href="structcutlass_1_1negate_3_01Array_3_01half__t_00_01N_01_4_01_4.html#aecf43e7a3abad362be771be2b98c7f71">cutlass::negate&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(Array&lt; half_t, N &gt; const &amp;lhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:993</div></div>
<div class="ttc" id="structcutlass_1_1minus_html"><div class="ttname"><a href="structcutlass_1_1minus.html">cutlass::minus</a></div><div class="ttdef"><b>Definition:</b> functional.h:55</div></div>
<div class="ttc" id="structcutlass_1_1multiply__add_3_01complex_3_01T_01_4_00_01complex_3_01T_01_4_00_01complex_3_01T_01_4_01_4_html_af46b43d672441fccd96c1aa3b89886fa"><div class="ttname"><a href="structcutlass_1_1multiply__add_3_01complex_3_01T_01_4_00_01complex_3_01T_01_4_00_01complex_3_01T_01_4_01_4.html#af46b43d672441fccd96c1aa3b89886fa">cutlass::multiply_add&lt; complex&lt; T &gt;, complex&lt; T &gt;, complex&lt; T &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE complex&lt; T &gt; operator()(complex&lt; T &gt; const &amp;a, complex&lt; T &gt; const &amp;b, complex&lt; T &gt; const &amp;c) const </div><div class="ttdef"><b>Definition:</b> functional.h:118</div></div>
<div class="ttc" id="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4_html_a1cb3ab75146da628432a46deef9babb5"><div class="ttname"><a href="structcutlass_1_1multiplies_3_01Array_3_01half__t_00_01N_01_4_01_4.html#a1cb3ab75146da628432a46deef9babb5">cutlass::multiplies&lt; Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(Array&lt; half_t, N &gt; const &amp;lhs, half_t const &amp;rhs) const </div><div class="ttdef"><b>Definition:</b> functional.h:846</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963_html_abc9dd51cad4f2997dae521fad8f5b486"><div class="ttname"><a href="structcutlass_1_1multiply__add_3_01Array_3_01half__t_00_01N_01_4_00_01Array_3_01half__t_00_01N_01adaeadb27c0e4439444709c0eb30963.html#abc9dd51cad4f2997dae521fad8f5b486">cutlass::multiply_add&lt; Array&lt; half_t, N &gt;, Array&lt; half_t, N &gt;, Array&lt; half_t, N &gt; &gt;::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Array&lt; half_t, N &gt; operator()(Array&lt; half_t, N &gt; const &amp;a, Array&lt; half_t, N &gt; const &amp;b, Array&lt; half_t, N &gt; const &amp;c) const </div><div class="ttdef"><b>Definition:</b> functional.h:1028</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
