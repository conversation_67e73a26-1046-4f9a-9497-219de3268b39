<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: fragment_iterator_volta_tensor_op.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_e7fd38dbfb1fb5decd4aa6571e13ec6b.html">warp</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">fragment_iterator_volta_tensor_op.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="fragment__iterator__volta__tensor__op_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="include_2cutlass_2gemm_2gemm_8h.html">cutlass/gemm/gemm.h</a>&quot;</span></div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="volta__tensor__op__policy_8h.html">cutlass/epilogue/warp/volta_tensor_op_policy.h</a>&quot;</span></div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="keyword">namespace </span>warp {</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;  <span class="keyword">typename</span> WarpShape,             </div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <span class="keyword">typename</span> InterleavedTileShape,  </div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  <span class="keyword">typename</span> ElementC,              </div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;  <span class="keyword">typename</span> Layout                 </div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;&gt;</div><div class="line"><a name="l00061"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp.html">   61</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp.html">FragmentIteratorVoltaTensorOp</a>;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <span class="keyword">typename</span> WarpShape_         </div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;&gt;</div><div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html">   69</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp.html">FragmentIteratorVoltaTensorOp</a>&lt;WarpShape_, gemm::GemmShape&lt;32, 32, 4&gt;, <a class="code" href="structcutlass_1_1half__t.html">half_t</a>, <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>&gt; {</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#aaba83a583ffca1e7191df5db144f3693">   72</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#aaba83a583ffca1e7191df5db144f3693">WarpShape</a> = WarpShape_;</div><div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#ae7de02d970a2de2003f5340c0e1c6432">   73</a></span>&#160;  <span class="keyword">using</span> InterleavedTileShape = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;32, 32, 4&gt;</a>;</div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a3fde5885720080618e49e2f8f733c1ef">   74</a></span>&#160;  <span class="keyword">using</span> ElementC = <a class="code" href="structcutlass_1_1half__t.html">half_t</a>;</div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#aeae5ca1155296bf2eb1cb54cf860f4f2">   75</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a64367042cbb3b202b98f8b02a07b2de2">   78</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">Policy</a> = <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">VoltaTensorOpPolicy&lt;WarpShape, InterleavedTileShape, ElementC, Layout&gt;</a>;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a8d8984d62b6fda3dae79561eb4c96176">   81</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a8d8984d62b6fda3dae79561eb4c96176">AccessType</a> = <span class="keyword">typename</span> Policy::AccessType;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  </div><div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a99fbac47851323828ec2a9617014e6b3">   84</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a99fbac47851323828ec2a9617014e6b3">Fragment</a> = <span class="keyword">typename</span> Policy::Fragment;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a1885e99c86f4e32e9fb5e70a2925a6c7">   87</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a1885e99c86f4e32e9fb5e70a2925a6c7">AccumulatorTile</a> = <span class="keyword">typename</span> Policy::AccumulatorTile;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00089"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a65cd700f0eab0bf6ea647039a158def2">   89</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a65cd700f0eab0bf6ea647039a158def2">OutputAccumulatorTile</a> = <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a1885e99c86f4e32e9fb5e70a2925a6c7">AccumulatorTile</a>;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a7bde70907d0ef820ef36d771b9b9c247">   92</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = Policy::kIterations;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a8d8984d62b6fda3dae79561eb4c96176">AccessType</a> <span class="keyword">const</span> *accumulators_;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;  <span class="keywordtype">int</span> index_;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00112"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a11fda4be46e143b204c0a6e2325d8490">  112</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a11fda4be46e143b204c0a6e2325d8490">FragmentIteratorVoltaTensorOp</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a1885e99c86f4e32e9fb5e70a2925a6c7">AccumulatorTile</a> <span class="keyword">const</span> &amp;accum): </div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    accumulators_(reinterpret_cast&lt;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a8d8984d62b6fda3dae79561eb4c96176">AccessType</a> const *&gt;(&amp;accum)), </div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;    index_(0) {</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;  }</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a69c832f3eb6db78a28910a8f8fba2383">  120</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp.html">FragmentIteratorVoltaTensorOp</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a69c832f3eb6db78a28910a8f8fba2383">operator++</a>() {</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    ++index_;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;  }</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a9c9998058fa65fbf0f4ff4b73658e3d8">  127</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp.html">FragmentIteratorVoltaTensorOp</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a9c9998058fa65fbf0f4ff4b73658e3d8">operator--</a>() {</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    --index_;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  }</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00134"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a5a5121d6ae4256b7928fde6e8be74e6a">  134</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a5a5121d6ae4256b7928fde6e8be74e6a">load</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a99fbac47851323828ec2a9617014e6b3">Fragment</a> &amp;frag, <span class="keywordtype">int</span> index_offset = 0)<span class="keyword"> const </span>{</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a8d8984d62b6fda3dae79561eb4c96176">AccessType</a> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a8d8984d62b6fda3dae79561eb4c96176">AccessType</a> *<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessesPerMma = Policy::kElementsPerMma / Policy::kElementsPerAccess;</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> tile_n = 0; tile_n &lt; Policy::TileIterations::kColumn; ++tile_n) {</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;      </div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;      <span class="keywordtype">int</span> tile_access_idx = </div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        (tile_n * Policy::TileIterations::kRow + (index_ &amp; 2) / 2) * Policy::MmaIterations::kCount * kAccessesPerMma;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> mma_n = 0; mma_n &lt; Policy::MmaIterations::kColumn * kAccessesPerMma; ++mma_n) {</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        <span class="keywordtype">int</span> mma_access_idx = ((mma_n &amp; 1) * 2 + (index_ &amp; 1)) * kAccessesPerMma + (mma_n &amp; 2) / 2;</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;        frag_ptr[tile_n * Policy::MmaIterations::kColumn * kAccessesPerMma +</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;          mma_n] = accumulators_[tile_access_idx + mma_access_idx];</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;      }</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    }</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;  }</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;};</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;  <span class="keyword">typename</span> WarpShape_         </div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;&gt;</div><div class="line"><a name="l00164"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html">  164</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp.html">FragmentIteratorVoltaTensorOp</a>&lt;WarpShape_, gemm::GemmShape&lt;32, 32, 4&gt;, float, <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>&gt; {</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div><div class="line"><a name="l00167"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a08bdb5eb26059d10175a46e0aeecb382">  167</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a08bdb5eb26059d10175a46e0aeecb382">WarpShape</a> = WarpShape_;</div><div class="line"><a name="l00168"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a71122ebe81d816eabfc45a0b56572051">  168</a></span>&#160;  <span class="keyword">using</span> InterleavedTileShape = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;32, 32, 4&gt;</a>;</div><div class="line"><a name="l00169"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ab2c2e49abcf3d9f3c00960bdc87c357d">  169</a></span>&#160;  <span class="keyword">using</span> ElementC = float;</div><div class="line"><a name="l00170"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a7602a39bc56a4f7ec66085093bab92ca">  170</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div><div class="line"><a name="l00173"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae776e5f6feac3733565fb81c52725f2c">  173</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">Policy</a> = <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">VoltaTensorOpPolicy&lt;WarpShape, InterleavedTileShape, ElementC, Layout&gt;</a>;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;</div><div class="line"><a name="l00176"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae2cc318859541f821667086d89ae48f2">  176</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae2cc318859541f821667086d89ae48f2">AccessType</a> = <span class="keyword">typename</span> Policy::AccessType;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;  </div><div class="line"><a name="l00179"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a67a41ee64e7d20e92f5841926fabf9a1">  179</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a67a41ee64e7d20e92f5841926fabf9a1">Fragment</a> = <span class="keyword">typename</span> Policy::Fragment;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;</div><div class="line"><a name="l00182"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a5cb7ad40118423ef5d0cdd7cd5e62d79">  182</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a5cb7ad40118423ef5d0cdd7cd5e62d79">AccumulatorTile</a> = <span class="keyword">typename</span> Policy::AccumulatorTile;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;</div><div class="line"><a name="l00185"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a78d4cbe9dd5a59d6a220c5c761b522ce">  185</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = Policy::kIterations;</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae2cc318859541f821667086d89ae48f2">AccessType</a> <span class="keyword">const</span> *accumulators_;</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;  <span class="keywordtype">int</span> index_;</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00205"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae7d7fe96fc26493283a5b3b35c9f1c56">  205</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae7d7fe96fc26493283a5b3b35c9f1c56">FragmentIteratorVoltaTensorOp</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a5cb7ad40118423ef5d0cdd7cd5e62d79">AccumulatorTile</a> <span class="keyword">const</span> &amp;accum): </div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;    accumulators_(reinterpret_cast&lt;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae2cc318859541f821667086d89ae48f2">AccessType</a> const *&gt;(&amp;accum)), </div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;    index_(0) {</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;  }</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00212"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae3b3c2db69782cfa3a1720e999901ae9">  212</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp.html">FragmentIteratorVoltaTensorOp</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae3b3c2db69782cfa3a1720e999901ae9">operator++</a>() {</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;    ++index_;</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;  }</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00219"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a331cd6e1757dacbcc8461fcd2d0e6139">  219</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp.html">FragmentIteratorVoltaTensorOp</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a331cd6e1757dacbcc8461fcd2d0e6139">operator--</a>() {</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;    --index_;</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;  }</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00226"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a13b3d46be4c12bec05be608e2fae1dcd">  226</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a13b3d46be4c12bec05be608e2fae1dcd">load</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a67a41ee64e7d20e92f5841926fabf9a1">Fragment</a> &amp;frag, <span class="keywordtype">int</span> index_offset = 0)<span class="keyword"> const </span>{</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae2cc318859541f821667086d89ae48f2">AccessType</a> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae2cc318859541f821667086d89ae48f2">AccessType</a> *<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;    <span class="keywordtype">int</span> <span class="keyword">const</span> kRegsPerMmaRow = 2;</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;      </div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> reg_row = 0; reg_row &lt; Policy::kRowsPerMmaTile; ++reg_row) {</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> tile_n = 0; tile_n &lt; Policy::TileIterations::kColumn; ++tile_n) {</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;    </div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;        <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> mma_n = 0; mma_n &lt; Policy::MmaIterations::kColumn * 2; ++mma_n) {</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;          <span class="keywordtype">int</span> mma_idx = (index_ &amp; 1) + (index_ &amp; 2) * Policy::MmaIterations::kCount / 2 +</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;            (tile_n * Policy::TileIterations::kRow) * Policy::MmaIterations::kCount + (mma_n &amp; 1) * 2;</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;          <span class="keywordtype">int</span> reg_offset = reg_row * kRegsPerMmaRow + (mma_n &amp; 2) * 2;</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;          <span class="keywordtype">int</span> reg_idx = mma_idx * Policy::kElementsPerMma + reg_offset;</div><div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;          *frag_ptr = accumulators_[reg_idx / Policy::kElementsPerAccess];</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;          ++frag_ptr;</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;        }</div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;      }</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;    }</div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;  }</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;};</div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;</div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;</div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;} <span class="comment">// namespace warp</span></div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;</div><div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2_html_a5cb7ad40118423ef5d0cdd7cd5e62d79"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a5cb7ad40118423ef5d0cdd7cd5e62d79">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::AccumulatorTile</a></div><div class="ttdeci">typename Policy::AccumulatorTile AccumulatorTile</div><div class="ttdoc">This is the complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:182</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b_html_a69c832f3eb6db78a28910a8f8fba2383"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a69c832f3eb6db78a28910a8f8fba2383">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorVoltaTensorOp &amp; operator++()</div><div class="ttdoc">Increments. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:120</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2_html_ae2cc318859541f821667086d89ae48f2"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae2cc318859541f821667086d89ae48f2">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::AccessType</a></div><div class="ttdeci">typename Policy::AccessType AccessType</div><div class="ttdoc">Array type for aligned memory accesses. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:176</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2_html_a331cd6e1757dacbcc8461fcd2d0e6139"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a331cd6e1757dacbcc8461fcd2d0e6139">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorVoltaTensorOp &amp; operator--()</div><div class="ttdoc">Decrements. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:219</div></div>
<div class="ttc" id="structcutlass_1_1half__t_html"><div class="ttname"><a href="structcutlass_1_1half__t.html">cutlass::half_t</a></div><div class="ttdoc">IEEE half-precision floating-point type. </div><div class="ttdef"><b>Definition:</b> half.h:126</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b_html_a1885e99c86f4e32e9fb5e70a2925a6c7"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a1885e99c86f4e32e9fb5e70a2925a6c7">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::AccumulatorTile</a></div><div class="ttdeci">typename Policy::AccumulatorTile AccumulatorTile</div><div class="ttdoc">This is the complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:87</div></div>
<div class="ttc" id="include_2cutlass_2gemm_2gemm_8h_html"><div class="ttname"><a href="include_2cutlass_2gemm_2gemm_8h.html">gemm.h</a></div><div class="ttdoc">Defines common types used for all GEMM-like operators. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b_html_a65cd700f0eab0bf6ea647039a158def2"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a65cd700f0eab0bf6ea647039a158def2">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::OutputAccumulatorTile</a></div><div class="ttdeci">AccumulatorTile OutputAccumulatorTile</div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:89</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2_html_ae3b3c2db69782cfa3a1720e999901ae9"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae3b3c2db69782cfa3a1720e999901ae9">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorVoltaTensorOp &amp; operator++()</div><div class="ttdoc">Increments. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:212</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2_html_a08bdb5eb26059d10175a46e0aeecb382"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a08bdb5eb26059d10175a46e0aeecb382">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::WarpShape</a></div><div class="ttdeci">WarpShape_ WarpShape</div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:167</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2_html_ae7d7fe96fc26493283a5b3b35c9f1c56"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#ae7d7fe96fc26493283a5b3b35c9f1c56">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::FragmentIteratorVoltaTensorOp</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorVoltaTensorOp(AccumulatorTile const &amp;accum)</div><div class="ttdoc">Constructs an iterator. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:205</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b_html_a8d8984d62b6fda3dae79561eb4c96176"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a8d8984d62b6fda3dae79561eb4c96176">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::AccessType</a></div><div class="ttdeci">typename Policy::AccessType AccessType</div><div class="ttdoc">Array type for aligned memory accesses. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:81</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b_html_a9c9998058fa65fbf0f4ff4b73658e3d8"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a9c9998058fa65fbf0f4ff4b73658e3d8">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::operator--</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorVoltaTensorOp &amp; operator--()</div><div class="ttdoc">Decrements. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:127</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b_html_a5a5121d6ae4256b7928fde6e8be74e6a"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a5a5121d6ae4256b7928fde6e8be74e6a">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::load</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load(Fragment &amp;frag, int index_offset=0) const </div><div class="ttdoc">Loads a fragment from the referenced part of the accumulator tile. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:134</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp.html">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp</a></div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:61</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape</a></div><div class="ttdoc">Shape of a matrix multiply-add operation. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:57</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2_html_a13b3d46be4c12bec05be608e2fae1dcd"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a13b3d46be4c12bec05be608e2fae1dcd">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::load</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load(Fragment &amp;frag, int index_offset=0) const </div><div class="ttdoc">Loads a fragment from the referenced part of the accumulator tile. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:226</div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">cutlass::epilogue::warp::VoltaTensorOpPolicy</a></div><div class="ttdoc">Policy details related to the epilogue. </div><div class="ttdef"><b>Definition:</b> volta_tensor_op_policy.h:52</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b_html_a11fda4be46e143b204c0a6e2325d8490"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a11fda4be46e143b204c0a6e2325d8490">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::FragmentIteratorVoltaTensorOp</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE FragmentIteratorVoltaTensorOp(AccumulatorTile const &amp;accum)</div><div class="ttdoc">Constructs an iterator. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:112</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b_html_aaba83a583ffca1e7191df5db144f3693"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#aaba83a583ffca1e7191df5db144f3693">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::WarpShape</a></div><div class="ttdeci">WarpShape_ WarpShape</div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:72</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b_html_a99fbac47851323828ec2a9617014e6b3"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1G16e08718cffa0989cce3fe8dbc4b075b.html#a99fbac47851323828ec2a9617014e6b3">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::Fragment</a></div><div class="ttdeci">typename Policy::Fragment Fragment</div><div class="ttdoc">This is the fragment size produced by one access of the iterator. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:84</div></div>
<div class="ttc" id="volta__tensor__op__policy_8h_html"><div class="ttname"><a href="volta__tensor__op__policy_8h.html">volta_tensor_op_policy.h</a></div><div class="ttdoc">Defines basic structures needed for implementing the warp-scoped phase of the epilogue. These quantities assume a &amp;#39;column-major&amp;#39; arrangement of TensorOp instructions, of which a row-oriented slice is visible per iteration. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2_html_a67a41ee64e7d20e92f5841926fabf9a1"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1FragmentIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gdb805a2dc5571ac3b66e0fe6ffdcede2.html#a67a41ee64e7d20e92f5841926fabf9a1">cutlass::epilogue::warp::FragmentIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::Fragment</a></div><div class="ttdeci">typename Policy::Fragment Fragment</div><div class="ttdoc">This is the fragment size produced by one access of the iterator. </div><div class="ttdef"><b>Definition:</b> fragment_iterator_volta_tensor_op.h:179</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
