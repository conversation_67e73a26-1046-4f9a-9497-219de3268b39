<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: exceptions.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">exceptions.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="exceptions_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/******************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2011-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * modification, are not permitted.</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> * DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE FOR ANY</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> ******************************************************************************/</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &lt;cuda_runtime.h&gt;</span></div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#include &lt;iosfwd&gt;</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#include &lt;stdexcept&gt;</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="platform_8h.html">cutlass/platform/platform.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno"><a class="line" href="classcutlass_1_1cuda__exception.html">   36</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1cuda__exception.html">cuda_exception</a> : <span class="keyword">public</span> std::exception {</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00039"></a><span class="lineno"><a class="line" href="classcutlass_1_1cuda__exception.html#acbfb99c9979ce7d24fe774459c66cfa5">   39</a></span>&#160;  <a class="code" href="classcutlass_1_1cuda__exception.html#acbfb99c9979ce7d24fe774459c66cfa5">cuda_exception</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="classcutlass_1_1cuda__exception.html#af748a69a87ad9863985f6a77260ba77a">msg</a> = <span class="stringliteral">&quot;&quot;</span>, cudaError_t <a class="code" href="classcutlass_1_1cuda__exception.html#a1166c2a5331dbf394abd5309b4d1377a">err</a> = cudaErrorUnknown) : <a class="code" href="classcutlass_1_1cuda__exception.html#af748a69a87ad9863985f6a77260ba77a">msg</a>(<a class="code" href="classcutlass_1_1cuda__exception.html#af748a69a87ad9863985f6a77260ba77a">msg</a>), <a class="code" href="classcutlass_1_1cuda__exception.html#a1166c2a5331dbf394abd5309b4d1377a">err</a>(<a class="code" href="classcutlass_1_1cuda__exception.html#a1166c2a5331dbf394abd5309b4d1377a">err</a>) {}</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno"><a class="line" href="classcutlass_1_1cuda__exception.html#a5e2363c04ed0a43e244b274cb21aebf1">   42</a></span>&#160;  cudaError_t <a class="code" href="classcutlass_1_1cuda__exception.html#a5e2363c04ed0a43e244b274cb21aebf1">cudaError</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1cuda__exception.html#a1166c2a5331dbf394abd5309b4d1377a">err</a>; }</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; <span class="keyword">protected</span>:</div><div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="classcutlass_1_1cuda__exception.html#af748a69a87ad9863985f6a77260ba77a">   46</a></span>&#160;  <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="classcutlass_1_1cuda__exception.html#af748a69a87ad9863985f6a77260ba77a">msg</a>;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00049"></a><span class="lineno"><a class="line" href="classcutlass_1_1cuda__exception.html#a1166c2a5331dbf394abd5309b4d1377a">   49</a></span>&#160;  cudaError_t <a class="code" href="classcutlass_1_1cuda__exception.html#a1166c2a5331dbf394abd5309b4d1377a">err</a>;</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;};</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="namespacecutlass.html#acd972e1b9d8a8a5093167fa0b98c5b60">   53</a></span>&#160;<span class="keyword">inline</span> std::ostream&amp; <a class="code" href="namespacecutlass.html#addd443fc82f2acf867a61acff6c1cd29">operator&lt;&lt;</a>(std::ostream&amp; out, cudaError_t result) {</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;  <span class="keywordflow">return</span> out &lt;&lt; cudaGetErrorString(result);</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;}</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00058"></a><span class="lineno"><a class="line" href="namespacecutlass.html#a72666c1b605c515f2fd895c6fd0e8e09">   58</a></span>&#160;<span class="keyword">inline</span> std::ostream&amp; <a class="code" href="namespacecutlass.html#addd443fc82f2acf867a61acff6c1cd29">operator&lt;&lt;</a>(std::ostream&amp; out, <a class="code" href="classcutlass_1_1cuda__exception.html">cuda_exception</a> <span class="keyword">const</span>&amp; e) {</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;  <span class="keywordflow">return</span> out &lt;&lt; e.what() &lt;&lt; <span class="stringliteral">&quot;: &quot;</span> &lt;&lt; e.<a class="code" href="classcutlass_1_1cuda__exception.html#a5e2363c04ed0a43e244b274cb21aebf1">cudaError</a>();</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;}</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;}  <span class="comment">// namespace cutlass</span></div><div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1cuda__exception_html_a5e2363c04ed0a43e244b274cb21aebf1"><div class="ttname"><a href="classcutlass_1_1cuda__exception.html#a5e2363c04ed0a43e244b274cb21aebf1">cutlass::cuda_exception::cudaError</a></div><div class="ttdeci">cudaError_t cudaError() const </div><div class="ttdoc">Returns the underlying CUDA cudaError_t. </div><div class="ttdef"><b>Definition:</b> exceptions.h:42</div></div>
<div class="ttc" id="classcutlass_1_1cuda__exception_html_acbfb99c9979ce7d24fe774459c66cfa5"><div class="ttname"><a href="classcutlass_1_1cuda__exception.html#acbfb99c9979ce7d24fe774459c66cfa5">cutlass::cuda_exception::cuda_exception</a></div><div class="ttdeci">cuda_exception(const char *msg=&quot;&quot;, cudaError_t err=cudaErrorUnknown)</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> exceptions.h:39</div></div>
<div class="ttc" id="platform_8h_html"><div class="ttname"><a href="platform_8h.html">platform.h</a></div><div class="ttdoc">C++ features that may be otherwise unimplemented for CUDA device functions. </div></div>
<div class="ttc" id="classcutlass_1_1cuda__exception_html_af748a69a87ad9863985f6a77260ba77a"><div class="ttname"><a href="classcutlass_1_1cuda__exception.html#af748a69a87ad9863985f6a77260ba77a">cutlass::cuda_exception::msg</a></div><div class="ttdeci">const char * msg</div><div class="ttdoc">Explanatory string. </div><div class="ttdef"><b>Definition:</b> exceptions.h:46</div></div>
<div class="ttc" id="namespacecutlass_html_addd443fc82f2acf867a61acff6c1cd29"><div class="ttname"><a href="namespacecutlass.html#addd443fc82f2acf867a61acff6c1cd29">cutlass::operator&lt;&lt;</a></div><div class="ttdeci">std::ostream &amp; operator&lt;&lt;(std::ostream &amp;out, complex&lt; T &gt; const &amp;z)</div><div class="ttdef"><b>Definition:</b> complex.h:291</div></div>
<div class="ttc" id="classcutlass_1_1cuda__exception_html"><div class="ttname"><a href="classcutlass_1_1cuda__exception.html">cutlass::cuda_exception</a></div><div class="ttdoc">C++ exception wrapper for CUDA cudaError_t. </div><div class="ttdef"><b>Definition:</b> exceptions.h:36</div></div>
<div class="ttc" id="classcutlass_1_1cuda__exception_html_a1166c2a5331dbf394abd5309b4d1377a"><div class="ttname"><a href="classcutlass_1_1cuda__exception.html#a1166c2a5331dbf394abd5309b4d1377a">cutlass::cuda_exception::err</a></div><div class="ttdeci">cudaError_t err</div><div class="ttdoc">Underlying CUDA cudaError_t. </div><div class="ttdef"><b>Definition:</b> exceptions.h:49</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
