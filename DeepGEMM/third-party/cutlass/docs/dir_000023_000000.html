<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: host -&gt; include Relation</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li><li class="navelem"><a class="el" href="dir_01de8928c960cafb028e5f164701e1de.html">reference</a></li><li class="navelem"><a class="el" href="dir_b790a865367d69962c5919afdba4a959.html">host</a></li>  </ul>
</div>
</div><!-- top -->
<div class="contents">
<h3>host &rarr; include Relation</h3><table class="dirtab"><tr class="dirtab"><th class="dirtab">File in tools/util/include/cutlass/util/reference/host</th><th class="dirtab">Includes file in include</th></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="host_2tensor__compare_8h.html">host/tensor_compare.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="cutlass_8h.html">cutlass.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="host_2tensor__elementwise_8h.html">host/tensor_elementwise.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="cutlass_8h.html">cutlass.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="host_2tensor__elementwise_8h.html">host/tensor_elementwise.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="functional_8h.html">functional.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="host_2tensor__fill_8h.html">host/tensor_fill.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="array_8h.html">array.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="host_2tensor__fill_8h.html">host/tensor_fill.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="complex_8h.html">complex.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="host_2tensor__fill_8h.html">host/tensor_fill.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="cutlass_8h.html">cutlass.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="host_2tensor__fill_8h.html">host/tensor_fill.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="numeric__types_8h.html">numeric_types.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="host_2tensor__foreach_8h.html">host/tensor_foreach.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="cutlass_8h.html">cutlass.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tensor__copy_8h.html">tensor_copy.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="cutlass_8h.html">cutlass.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tensor__norm_8h.html">tensor_norm.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="complex_8h.html">complex.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tensor__norm_8h.html">tensor_norm.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="cutlass_8h.html">cutlass.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tensor__norm_8h.html">tensor_norm.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="tensor__ref_8h.html">tensor_ref.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm_8h.html">tools/util/include/cutlass/util/reference/host/gemm.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="dir_048c1df36ab9c2efbb0733edba6291c9.html">arch</a>&#160;/&#160;<a class="el" href="arch_2mma_8h.html">arch/mma.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm_8h.html">tools/util/include/cutlass/util/reference/host/gemm.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="coord_8h.html">coord.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm_8h.html">tools/util/include/cutlass/util/reference/host/gemm.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="functional_8h.html">functional.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm_8h.html">tools/util/include/cutlass/util/reference/host/gemm.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="dir_9aa36bd9cfad59a1f88859a38871c977.html">gemm</a>&#160;/&#160;<a class="el" href="include_2cutlass_2gemm_2gemm_8h.html">include/cutlass/gemm/gemm.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm_8h.html">tools/util/include/cutlass/util/reference/host/gemm.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="matrix__traits_8h.html">matrix_traits.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm_8h.html">tools/util/include/cutlass/util/reference/host/gemm.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="numeric__conversion_8h.html">numeric_conversion.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm_8h.html">tools/util/include/cutlass/util/reference/host/gemm.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="numeric__types_8h.html">numeric_types.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm_8h.html">tools/util/include/cutlass/util/reference/host/gemm.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="tensor__view_8h.html">tensor_view.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm__complex_8h.html">tools/util/include/cutlass/util/reference/host/gemm_complex.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="complex_8h.html">complex.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm__complex_8h.html">tools/util/include/cutlass/util/reference/host/gemm_complex.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="coord_8h.html">coord.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm__complex_8h.html">tools/util/include/cutlass/util/reference/host/gemm_complex.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="functional_8h.html">functional.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm__complex_8h.html">tools/util/include/cutlass/util/reference/host/gemm_complex.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="dir_9aa36bd9cfad59a1f88859a38871c977.html">gemm</a>&#160;/&#160;<a class="el" href="include_2cutlass_2gemm_2gemm_8h.html">include/cutlass/gemm/gemm.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm__complex_8h.html">tools/util/include/cutlass/util/reference/host/gemm_complex.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="matrix__traits_8h.html">matrix_traits.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm__complex_8h.html">tools/util/include/cutlass/util/reference/host/gemm_complex.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="numeric__conversion_8h.html">numeric_conversion.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm__complex_8h.html">tools/util/include/cutlass/util/reference/host/gemm_complex.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="numeric__types_8h.html">numeric_types.h</a></td></tr><tr class="dirtab"><td class="dirtab"><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2host_2gemm__complex_8h.html">tools/util/include/cutlass/util/reference/host/gemm_complex.h</a></td><td class="dirtab"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a>&#160;/&#160;<a class="el" href="tensor__view_8h.html">tensor_view.h</a></td></tr></table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
