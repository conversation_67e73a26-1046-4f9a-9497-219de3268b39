<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: direct_epilogue_tensor_op.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_05a6795d99d74f63b7300fc6eb9e55c2.html">threadblock</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">direct_epilogue_tensor_op.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="direct__epilogue__tensor__op_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="include_2cutlass_2gemm_2gemm_8h.html">cutlass/gemm/gemm.h</a>&quot;</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="keyword">namespace </span>threadblock {</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;  <span class="keyword">typename</span> Shape_,            </div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  <span class="keyword">typename</span> Operator_,         </div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;  <span class="keywordtype">int</span> PartitionsK,            </div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  <span class="keyword">typename</span> Element_,          </div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;  <span class="keyword">typename</span> OutputOp_,         </div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;  <span class="keyword">typename</span> ConvertOp_         </div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;&gt;</div><div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html">   55</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html">DirectEpilogueTensorOp</a> {</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div><div class="line"><a name="l00058"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a7b418c651040fe885e179a5e51220770">   58</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a7b418c651040fe885e179a5e51220770">Shape</a> = Shape_;</div><div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#aa927856824f1200323c393afbb6f90ef">   59</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#aa927856824f1200323c393afbb6f90ef">Operator</a> = Operator_;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">WarpCount</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;    Shape::kM / Operator::Shape::kM,</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;    Shape::kN / Operator::Shape::kN,</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;    PartitionsK,</div><div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a231c86e48c558336736dcc58c2e9783a">   66</a></span>&#160;  &gt;;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(PartitionsK == 1, </div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;    <span class="stringliteral">&quot;Direct epilogue cannot be used with when the threadblock tile is partitioned along the K dimension.&quot;</span>);</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a4e03d7b83604c014d530353a67ba3356">   72</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a4e03d7b83604c014d530353a67ba3356">FragmentC</a> = <span class="keyword">typename</span> Operator::FragmentC;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a7444586033d2448bded6df955f1408aa">   75</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a7444586033d2448bded6df955f1408aa">Element</a> = Element_;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#affbcb5b699be059628d3d37a589b2667">   78</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a91c018d7741a34a36e45d6c708d0b23e">   81</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a91c018d7741a34a36e45d6c708d0b23e">OutputOp</a> = OutputOp_;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a71707c91f25720e027e9e3b9f7a8a113">   84</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a71707c91f25720e027e9e3b9f7a8a113">ConvertOp</a> = ConvertOp_;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#ad60aa76f8f35170b9fc9ee8e68d71ade">   87</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#ad60aa76f8f35170b9fc9ee8e68d71ade">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout::kRank, Layout&gt;</a>;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html">   92</a></span>&#160;  <span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html">Params</a> {</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;    <span class="comment">// Data members</span></div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a89d406e705b7817243e3aa9d4253bb14">   98</a></span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a89d406e705b7817243e3aa9d4253bb14">destination_ref</a>;</div><div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a154450b8a74d1f1bb62d3d9e3b330597">   99</a></span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a154450b8a74d1f1bb62d3d9e3b330597">source_ref</a>;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div><div class="line"><a name="l00101"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a0c230d7407585f6cd6221ebcd5966a0d">  101</a></span>&#160;    <span class="keyword">typename</span> OutputOp::Params <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a0c230d7407585f6cd6221ebcd5966a0d">output_op</a>;</div><div class="line"><a name="l00102"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a97f1044e7b7cec1ddd1f120b8b7539f2">  102</a></span>&#160;    <span class="keyword">typename</span> ConvertOp::Params <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a97f1044e7b7cec1ddd1f120b8b7539f2">convert_op</a>;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;    <span class="comment">// Methods</span></div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00110"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a7890b7910f0b680ff5b9230d91efc2ef">  110</a></span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a7890b7910f0b680ff5b9230d91efc2ef">Params</a>(</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;      <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> destination_ref_,</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;      <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> source_ref_,</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;      <span class="keyword">typename</span> OutputOp::Params output_op_,</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;      <span class="keyword">typename</span> ConvertOp::Params convert_op_</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;    ):</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;      destination_ref(destination_ref_),</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;      source_ref(source_ref_),</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;      output_op(output_op_),</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;      convert_op(convert_op_) {</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    }</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00125"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#ae9578b799d3eb7d566112abb51146ec2">  125</a></span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#ae9578b799d3eb7d566112abb51146ec2">Params</a>(</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;      <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> destination_ref_,</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;      <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> source_ref_,</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;      <span class="keyword">typename</span> OutputOp::Params output_op_</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    ): </div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;      <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html">Params</a>(</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;        destination_ref, </div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;        source_ref, </div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;        output_op, </div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;        <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a71707c91f25720e027e9e3b9f7a8a113">ConvertOp</a>::<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html">Params</a>()</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;      ) { }</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  };</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;</div><div class="line"><a name="l00139"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1SharedStorage.html">  139</a></span>&#160;  <span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1SharedStorage.html">SharedStorage</a> {  };</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a91c018d7741a34a36e45d6c708d0b23e">OutputOp</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a0c230d7407585f6cd6221ebcd5966a0d">output_op</a>;                 </div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a71707c91f25720e027e9e3b9f7a8a113">ConvertOp</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a97f1044e7b7cec1ddd1f120b8b7539f2">convert_op</a>;               </div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> destination_ref_;         </div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> source_ref_;              </div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;  <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> warp_origin_;           </div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00155"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a7a64b4523780869f4b7dde2225572b2f">  155</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a7a64b4523780869f4b7dde2225572b2f">DirectEpilogueTensorOp</a>(</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html">Params</a> <span class="keyword">const</span> &amp;params,             </div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1SharedStorage.html">SharedStorage</a> &amp;shared_storage,    </div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;    <span class="keywordtype">int</span> thread_idx,                   </div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;    <span class="keywordtype">int</span> warp_idx,                     </div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;    <span class="keywordtype">int</span> lane_idx                      </div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;  ):</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    output_op(params.output_op),</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;    convert_op(params.convert_op),</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    destination_ref_(params.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a89d406e705b7817243e3aa9d4253bb14">destination_ref</a>),</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;    source_ref_(params.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a154450b8a74d1f1bb62d3d9e3b330597">source_ref</a>) {</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    <span class="comment">// Compute warp location within threadblock tile by mapping the warp_id to three coordinates:</span></div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    <span class="comment">//   _m: the warp&#39;s position within the threadblock along the M dimension</span></div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    <span class="comment">//   _n: the warp&#39;s position within the threadblock along the N dimension</span></div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;    <span class="comment">//   _k: the warp&#39;s position within the threadblock along the K dimension</span></div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;    <span class="keywordtype">int</span> warp_mn = warp_idx % (<a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a7a47fe0c44571a0a68a43c5a47cf676a">WarpCount::kM</a> * <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a9fcbaa4b47b83d0c8a09979ad5c98a1e">WarpCount::kN</a>);</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;    <span class="keywordtype">int</span> warp_m = warp_mn % <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a7a47fe0c44571a0a68a43c5a47cf676a">WarpCount::kM</a>;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;    <span class="keywordtype">int</span> warp_n = warp_mn / <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a7a47fe0c44571a0a68a43c5a47cf676a">WarpCount::kM</a>;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    warp_origin_ = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>{</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;      warp_m * Operator::Shape::kM, </div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;      warp_n * Operator::Shape::kN</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;    };</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;    destination_ref_.<a class="code" href="classcutlass_1_1TensorRef.html#a4bed879c428963070de8ffbdc5d6e4f9">add_coord_offset</a>(warp_origin_);</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    source_ref_.<a class="code" href="classcutlass_1_1TensorRef.html#a4bed879c428963070de8ffbdc5d6e4f9">add_coord_offset</a>(warp_origin_);</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;  }</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00189"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a8a8dd685344c17952aed17bcd9d6b831">  189</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a8a8dd685344c17952aed17bcd9d6b831">operator()</a>(</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;    <a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size,             </div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;    <a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> tb_tile_coord,            </div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a4e03d7b83604c014d530353a67ba3356">FragmentC</a> <span class="keyword">const</span> &amp;accumulators) {    </div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;    <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> thread_origin = </div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;      <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>{tb_tile_coord.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a93515a41db6c4b7e9101067f60d41b8c">m</a>() * Shape::kM, tb_tile_coord.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a1b29d2cb15360ad5499216859ad5436a">n</a>() * Shape::kN} + warp_origin_;</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;    <span class="keyword">using</span> MmaIterations = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;      Operator::Shape::kM / Operator::Policy::Operator::Shape::kM,</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;      Operator::Shape::kN / Operator::Policy::Operator::Shape::kN</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;    &gt;;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;    <span class="comment">// Assume accumulator tile is an arrangement of 8-by-8 tiles replicated over the entire</span></div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    <span class="comment">// shape, with each quad mapped to one row and each thread mapped to 1/4 of the elements</span></div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;    <span class="comment">// of that row. The accumulators within one row are assumed to be consecutive.</span></div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;    <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = Operator::Policy::Operator::Shape::kN / 4;</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;    <span class="keywordtype">int</span> <span class="keyword">const</span> kRowsPerTile = 8;</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;    <span class="keywordtype">int</span> <span class="keyword">const</span> kAccumulatorRows = Operator::Policy::Operator::Shape::kM / kRowsPerTile;</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> mma_n = 0; mma_n &lt; MmaIterations::kN; ++mma_n) {</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> mma_m = 0; mma_m &lt; MmaIterations::kM; ++mma_m) {</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;        </div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;        <span class="keywordtype">int</span> mma_accum_start = kAccumulatorRows * kElementsPerAccess * </div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;          (mma_m * MmaIterations::kN + mma_n);</div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;        <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> row = 0; row &lt; kAccumulatorRows; ++row) {</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;          <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;          <span class="keywordflow">for</span> (<span class="keywordtype">int</span> col = 0; col &lt; kElementsPerAccess; ++col) {</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;            <span class="keywordtype">int</span> accum_m = mma_m * Operator::Policy::Operator::Shape::kM + row * kRowsPerTile;</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;            <span class="keywordtype">int</span> accum_n = mma_n * Operator::Policy::Operator::Shape::kN + col;</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;            <span class="keywordtype">int</span> idx = mma_accum_start + row * kElementsPerAccess + col;</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;            <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> accum_coord = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>{accum_m, accum_n};</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;            <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> thread_coord = thread_origin + accum_coord;</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;            <span class="keywordflow">if</span> (thread_coord &lt; <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>{problem_size.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a93515a41db6c4b7e9101067f60d41b8c">m</a>(), problem_size.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a1b29d2cb15360ad5499216859ad5436a">n</a>()}) {</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;              <span class="keyword">typename</span> ConvertOp::result_type converted_accum = <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a97f1044e7b7cec1ddd1f120b8b7539f2">convert_op</a>(accumulators[idx]);</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;              <span class="keyword">typename</span> OutputOp::result_type output = <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a0c230d7407585f6cd6221ebcd5966a0d">output_op</a>(converted_accum, source_ref_.<a class="code" href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">at</a>(accum_coord));</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;              destination_ref_.<a class="code" href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">at</a>(accum_coord) = output;</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;            }</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;          }</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;        }</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;      }</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;    }</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;  }</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;};</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;} <span class="comment">// namespace threadblock</span></div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;</div><div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html">cutlass::epilogue::threadblock::DirectEpilogueTensorOp</a></div><div class="ttdoc">Epilogue operator. </div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:55</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html_a7a47fe0c44571a0a68a43c5a47cf676a"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html#a7a47fe0c44571a0a68a43c5a47cf676a">cutlass::gemm::GemmShape::kM</a></div><div class="ttdeci">static int const kM</div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:58</div></div>
<div class="ttc" id="structcutlass_1_1MatrixShape_html"><div class="ttname"><a href="structcutlass_1_1MatrixShape.html">cutlass::MatrixShape</a></div><div class="ttdoc">Describes the size of a matrix tile. </div><div class="ttdef"><b>Definition:</b> matrix_shape.h:42</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params_html_ae9578b799d3eb7d566112abb51146ec2"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#ae9578b799d3eb7d566112abb51146ec2">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params(TensorRef destination_ref_, TensorRef source_ref_, typename OutputOp::Params output_op_)</div><div class="ttdoc">Constructs a Params object. </div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:125</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::Params</a></div><div class="ttdoc">Parameters structure for host-constructible state. </div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:92</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_html_a8a8dd685344c17952aed17bcd9d6b831"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a8a8dd685344c17952aed17bcd9d6b831">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::operator()</a></div><div class="ttdeci">CUTLASS_DEVICE void operator()(gemm::GemmCoord problem_size, gemm::GemmCoord tb_tile_coord, FragmentC const &amp;accumulators)</div><div class="ttdoc">Streams the result to global memory. </div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:189</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params_html_a89d406e705b7817243e3aa9d4253bb14"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a89d406e705b7817243e3aa9d4253bb14">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::Params::destination_ref</a></div><div class="ttdeci">TensorRef destination_ref</div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:98</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html">cutlass::gemm::GemmCoord</a></div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:94</div></div>
<div class="ttc" id="include_2cutlass_2gemm_2gemm_8h_html"><div class="ttname"><a href="include_2cutlass_2gemm_2gemm_8h.html">gemm.h</a></div><div class="ttdoc">Defines common types used for all GEMM-like operators. </div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html_a1b29d2cb15360ad5499216859ad5436a"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html#a1b29d2cb15360ad5499216859ad5436a">cutlass::gemm::GemmCoord::n</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; n() const </div><div class="ttdoc">Returns the GEMM N coordinate. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:137</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_html_ad60aa76f8f35170b9fc9ee8e68d71ade"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#ad60aa76f8f35170b9fc9ee8e68d71ade">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::TensorRef</a></div><div class="ttdeci">TensorRef&lt; Element, Layout::kRank, Layout &gt; TensorRef</div><div class="ttdoc">Reference to source and destination tensors. </div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:87</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a4bed879c428963070de8ffbdc5d6e4f9"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a4bed879c428963070de8ffbdc5d6e4f9">cutlass::TensorRef::add_coord_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef &amp; add_coord_offset(TensorCoord const &amp;coord)</div><div class="ttdoc">Adds an offset to each pointer. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:326</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params_html_a154450b8a74d1f1bb62d3d9e3b330597"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a154450b8a74d1f1bb62d3d9e3b330597">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::Params::source_ref</a></div><div class="ttdeci">TensorRef source_ref</div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:99</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_html_a7a64b4523780869f4b7dde2225572b2f"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a7a64b4523780869f4b7dde2225572b2f">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::DirectEpilogueTensorOp</a></div><div class="ttdeci">CUTLASS_DEVICE DirectEpilogueTensorOp(Params const &amp;params, SharedStorage &amp;shared_storage, int thread_idx, int warp_idx, int lane_idx)</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:155</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params_html_a0c230d7407585f6cd6221ebcd5966a0d"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a0c230d7407585f6cd6221ebcd5966a0d">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::Params::output_op</a></div><div class="ttdeci">OutputOp::Params output_op</div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:101</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params_html_a97f1044e7b7cec1ddd1f120b8b7539f2"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a97f1044e7b7cec1ddd1f120b8b7539f2">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::Params::convert_op</a></div><div class="ttdeci">ConvertOp::Params convert_op</div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:102</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html"><div class="ttname"><a href="classcutlass_1_1TensorRef.html">cutlass::TensorRef&lt; Element, Layout::kRank, Layout &gt;</a></div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params_html_a7890b7910f0b680ff5b9230d91efc2ef"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1Params.html#a7890b7910f0b680ff5b9230d91efc2ef">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params(TensorRef destination_ref_, TensorRef source_ref_, typename OutputOp::Params output_op_, typename ConvertOp::Params convert_op_)</div><div class="ttdoc">Constructs a Params object. </div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:110</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1SharedStorage_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_1_1SharedStorage.html">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::SharedStorage</a></div><div class="ttdoc">Shared storage allocation needed by the epilogue. </div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:139</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape</a></div><div class="ttdoc">Shape of a matrix multiply-add operation. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:57</div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_html_a4e03d7b83604c014d530353a67ba3356"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a4e03d7b83604c014d530353a67ba3356">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::FragmentC</a></div><div class="ttdeci">typename Operator::FragmentC FragmentC</div><div class="ttdoc">Accumulator tile is really the warp-scoped tile. </div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:72</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_html_aa927856824f1200323c393afbb6f90ef"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#aa927856824f1200323c393afbb6f90ef">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::Operator</a></div><div class="ttdeci">Operator_ Operator</div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:59</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a8758907a1c9b1fcd00e7ece626d03b76"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">cutlass::TensorRef::at</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Reference at(TensorCoord const &amp;coord) const </div><div class="ttdoc">Returns a reference to the element at a given Coord. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:307</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_html_a91c018d7741a34a36e45d6c708d0b23e"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a91c018d7741a34a36e45d6c708d0b23e">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::OutputOp</a></div><div class="ttdeci">OutputOp_ OutputOp</div><div class="ttdoc">Function operator computing final output. </div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:81</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html_a93515a41db6c4b7e9101067f60d41b8c"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html#a93515a41db6c4b7e9101067f60d41b8c">cutlass::gemm::GemmCoord::m</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; m() const </div><div class="ttdoc">Returns the GEMM M coordinate. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:129</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_html_a71707c91f25720e027e9e3b9f7a8a113"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a71707c91f25720e027e9e3b9f7a8a113">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::ConvertOp</a></div><div class="ttdeci">ConvertOp_ ConvertOp</div><div class="ttdoc">Conversion operator to shared memory. </div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:84</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html">cutlass::MatrixCoord</a></div><div class="ttdef"><b>Definition:</b> matrix_coord.h:39</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_html_a7444586033d2448bded6df955f1408aa"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a7444586033d2448bded6df955f1408aa">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::Element</a></div><div class="ttdeci">Element_ Element</div><div class="ttdoc">Data type of output tensor. </div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:75</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp_html_a7b418c651040fe885e179a5e51220770"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a7b418c651040fe885e179a5e51220770">cutlass::epilogue::threadblock::DirectEpilogueTensorOp::Shape</a></div><div class="ttdeci">Shape_ Shape</div><div class="ttdef"><b>Definition:</b> direct_epilogue_tensor_op.h:58</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html_a9fcbaa4b47b83d0c8a09979ad5c98a1e"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html#a9fcbaa4b47b83d0c8a09979ad5c98a1e">cutlass::gemm::GemmShape::kN</a></div><div class="ttdeci">static int const kN</div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:59</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
