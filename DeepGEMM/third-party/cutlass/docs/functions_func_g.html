<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li class="current"><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_func.html"><span>Functions</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
      <li><a href="functions_type.html"><span>Typedefs</span></a></li>
      <li><a href="functions_enum.html"><span>Enumerations</span></a></li>
      <li><a href="functions_eval.html"><span>Enumerator</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="functions_func.html#index_a"><span>a</span></a></li>
      <li><a href="functions_func_b.html#index_b"><span>b</span></a></li>
      <li><a href="functions_func_c.html#index_c"><span>c</span></a></li>
      <li><a href="functions_func_d.html#index_d"><span>d</span></a></li>
      <li><a href="functions_func_e.html#index_e"><span>e</span></a></li>
      <li><a href="functions_func_f.html#index_f"><span>f</span></a></li>
      <li class="current"><a href="functions_func_g.html#index_g"><span>g</span></a></li>
      <li><a href="functions_func_h.html#index_h"><span>h</span></a></li>
      <li><a href="functions_func_i.html#index_i"><span>i</span></a></li>
      <li><a href="functions_func_k.html#index_k"><span>k</span></a></li>
      <li><a href="functions_func_l.html#index_l"><span>l</span></a></li>
      <li><a href="functions_func_m.html#index_m"><span>m</span></a></li>
      <li><a href="functions_func_n.html#index_n"><span>n</span></a></li>
      <li><a href="functions_func_o.html#index_o"><span>o</span></a></li>
      <li><a href="functions_func_p.html#index_p"><span>p</span></a></li>
      <li><a href="functions_func_q.html#index_q"><span>q</span></a></li>
      <li><a href="functions_func_r.html#index_r"><span>r</span></a></li>
      <li><a href="functions_func_s.html#index_s"><span>s</span></a></li>
      <li><a href="functions_func_t.html#index_t"><span>t</span></a></li>
      <li><a href="functions_func_u.html#index_u"><span>u</span></a></li>
      <li><a href="functions_func_v.html#index_v"><span>v</span></a></li>
      <li><a href="functions_func_w.html#index_w"><span>w</span></a></li>
      <li><a href="functions_func_0x7e.html#index_0x7e"><span>~</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_g"></a>- g -</h3><ul>
<li>Gemm()
: <a class="el" href="classcutlass_1_1gemm_1_1device_1_1Gemm.html#aec04d65c6265eb5f63d703f2dd99cb3f">cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, LayoutC_, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;</a>
, <a class="el" href="classcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layout4d0960ae6b1d1bf19e6239dbd002249c.html#abcacf502806db50eb17a6d925aee16d5">cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;</a>
, <a class="el" href="structcutlass_1_1gemm_1_1kernel_1_1Gemm.html#a4691db0e882a0392f7488709fe1c91ff">cutlass::gemm::kernel::Gemm&lt; Mma_, Epilogue_, ThreadblockSwizzle_, SplitKSerial &gt;</a>
, <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a9c849673822f71c869e5deb21fa4560b">cutlass::reference::device::thread::Gemm&lt; TensorRefA, TensorRefB, TensorRefC, ScalarType, AccumulatorType, OutputTile, InnerProductOp, ConvertOp &gt;</a>
</li>
<li>GemmBatched()
: <a class="el" href="classcutlass_1_1gemm_1_1device_1_1GemmBatched.html#a72a26fb286181aa5ca1fb66d9b385f7f">cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, LayoutC_, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;</a>
, <a class="el" href="classcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_0c9bb6f4463ab6085e6008b5d5ad6abfd.html#a75922fd7bcd77fbc714cd87681f692bf">cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;</a>
, <a class="el" href="structcutlass_1_1gemm_1_1kernel_1_1GemmBatched.html#ae01da9be38c69a99e8d09b978b6cd267">cutlass::gemm::kernel::GemmBatched&lt; Mma_, Epilogue_, ThreadblockSwizzle_ &gt;</a>
</li>
<li>GemmComplex()
: <a class="el" href="classcutlass_1_1gemm_1_1device_1_1GemmComplex.html#a66f86867709e19e1807776c976755768">cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, LayoutC_, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;</a>
, <a class="el" href="classcutlass_1_1gemm_1_1device_1_1GemmComplex_3_01ElementA___00_01LayoutA___00_01ElementB___00_07c56401b4df75709ae636675d9980a9a.html#a9ce748bfc112dd4bb942c5e7c95845df">cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;</a>
</li>
<li>GemmCoord()
: <a class="el" href="structcutlass_1_1gemm_1_1GemmCoord.html#abaa87475d518a2e5cdf44c62122b9e01">cutlass::gemm::GemmCoord</a>
</li>
<li>GemmDescription()
: <a class="el" href="structcutlass_1_1library_1_1GemmDescription.html#a6907c6b21b9e5572eb803116a24f1d47">cutlass::library::GemmDescription</a>
</li>
<li>GemmHorizontalThreadblockSwizzle()
: <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmHorizontalThreadblockSwizzle.html#af1ec47b46e59cc2b88600e7396fa55c2">cutlass::gemm::threadblock::GemmHorizontalThreadblockSwizzle</a>
</li>
<li>GemmIdentityThreadblockSwizzle()
: <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmIdentityThreadblockSwizzle.html#aeefb397f601cd10a62c93d172f03ba4d">cutlass::gemm::threadblock::GemmIdentityThreadblockSwizzle</a>
</li>
<li>GemmSplitKParallel()
: <a class="el" href="classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel.html#abfb1166a9c55270ff8f1b265516a418c">cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, LayoutC_, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;</a>
, <a class="el" href="classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01ElementBbe7c1f7154ad5b5bf9d4d28301e2b457.html#ad0c614a548bcade989eb25633b45bb0f">cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;</a>
, <a class="el" href="structcutlass_1_1gemm_1_1kernel_1_1GemmSplitKParallel.html#aa0831b70d4c76337cffc040efe921ca7">cutlass::gemm::kernel::GemmSplitKParallel&lt; Mma_, Epilogue_, ThreadblockSwizzle_ &gt;</a>
</li>
<li>Gemv()
: <a class="el" href="classcutlass_1_1gemm_1_1threadblock_1_1Gemv.html#a62a894623fcd47724bd066e8026a1210">cutlass::gemm::threadblock::Gemv&lt; Core_ &gt;</a>
</li>
<li>GemvBatchedStridedEpilogueScaling()
: <a class="el" href="structcutlass_1_1gemm_1_1kernel_1_1detail_1_1GemvBatchedStridedEpilogueScaling.html#a55f36af0801edcbde757a0cbfae7229c">cutlass::gemm::kernel::detail::GemvBatchedStridedEpilogueScaling&lt; ElementAlphaBeta, BetaIsZero &gt;</a>
</li>
<li>GeneralMatrix()
: <a class="el" href="structcutlass_1_1layout_1_1GeneralMatrix.html#a1a8ec23dfd1bb09295f14a8cd67b6c77">cutlass::layout::GeneralMatrix</a>
</li>
<li>get()
: <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1const__reference.html#a37a90c6f1edcc3d7a916211aa7520cc1">cutlass::Array&lt; T, N, false &gt;::const_reference</a>
, <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4_1_1reference.html#a3bb74e5ee555773803b39cc478af5069">cutlass::Array&lt; T, N, false &gt;::reference</a>
, <a class="el" href="classcutlass_1_1ConstSubbyteReference.html#ae5af3bf12950795fdc96c1e65db31776">cutlass::ConstSubbyteReference&lt; Element_, Storage_ &gt;</a>
, <a class="el" href="structcutlass_1_1device__memory_1_1allocation.html#a86356748c671059942168502dd58684a">cutlass::device_memory::allocation&lt; T &gt;</a>
, <a class="el" href="classcutlass_1_1platform_1_1unique__ptr.html#a2e7c14b8a118f81c1df46ea5045e297b">cutlass::platform::unique_ptr&lt; T, Deleter &gt;</a>
, <a class="el" href="classcutlass_1_1PredicateVector_1_1ConstIterator.html#a5b9b8f338a12fb3954ded0e5927c5318">cutlass::PredicateVector&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::ConstIterator</a>
, <a class="el" href="classcutlass_1_1PredicateVector_1_1Iterator.html#af035589126434bd2dbef4000cd864b8b">cutlass::PredicateVector&lt; kPredicates_, kPredicatesPerByte_, kPredicateStart_ &gt;::Iterator</a>
, <a class="el" href="structcutlass_1_1ReferenceFactory_3_01Element_00_01false_01_4.html#a476101ee27000f24d9d86b2080bdd551">cutlass::ReferenceFactory&lt; Element, false &gt;</a>
, <a class="el" href="structcutlass_1_1ReferenceFactory_3_01Element_00_01true_01_4.html#a33d06a48e057013200ef7f806535bad7">cutlass::ReferenceFactory&lt; Element, true &gt;</a>
, <a class="el" href="classcutlass_1_1SubbyteReference.html#a284ab4f025b7ae2d1b0cbff5e79b6f98">cutlass::SubbyteReference&lt; Element_, Storage_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__da632779aba661c0f4cfaaa78126b771.html#ad130285bdc65963bad28c427935a7444">cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__1790abaa54a01f277d75766d5882fec8.html#a21a791f375ef3b9d94e4bea10b7bbdcf">cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__7327fa15996bcb8502cdfcc192350fe1.html#ab27ea2be4e2c962ffa8b72bd9d4ba935">cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen89c687c583745a73cb485041911a4c4e.html#ada0cc9d5c2245873fe7703556b36d974">cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemenab63a1e105bf37f6371516cb9e2c5a7a.html#a65054125754433c39aae334950891f2e">cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen784a0e9da3f55064c47e5613791f51f7.html#a8b8c2b72af740a2c60b972ae069d5b9d">cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen9838736ad62fae54213fbaf722a989ab.html#a24e206bff266d5f3e16a71d529a3bc7b">cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen809793e785fb4211888c6b4e5dcfcb39.html#a31b0f6d9d8cbc569bc518d304b81ce01">cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__eb7d20f8b9d69e0ae5e7ef51dc480867.html#a2644ce95a3d331e911a79d7e3f9c51ab">cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Alignment &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__2c1476eaf582bfe972793e17babfe985.html#ab3681ca017522ca95a379acc6c7ef791">cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__a3c11cf1f00ef7a1efb8389ac6e4c6e0.html#ae90040a003d5ea7a3521daaa098a233d">cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__0855e9d9ab619202d2397180c1e4c4a5.html#ab7d06942d29892e8214f977d4f81b869">cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Alignment &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__f04332958a49a47d6fb2b25201764630.html#a4b398e3cfe8d66fd494ea32839d6fdea">cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Alignment &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__6baada077236f1a368c61c5e11b45b72.html#aa76837c456b5dd36569863c6b4694243">cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__0184b7188941788a96624510a4b2f876.html#a71daea97d35fc66a496240371952f872">cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__ebf4714349612673e8b6609b763eeb6f.html#a2c91372a2ea9bbd5ebb6fc529bd64cd6">cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__e9a9e0f4286f652f55eb9b863b21effe.html#ae7f1dcf320dc23628a1e01bef93451c6">cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::TensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;</a>
</li>
<li>get_batch_idx()
: <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmBatchedIdentityThreadblockSwizzle.html#a9dc5fe45d0e19f9663de017b2acbbd62">cutlass::gemm::threadblock::GemmBatchedIdentityThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemvBatchedStridedThreadblockDefaultSwizzle.html#a6feba78b6a3e43b58a142f200c1d2b0c">cutlass::gemm::threadblock::GemvBatchedStridedThreadblockDefaultSwizzle</a>
</li>
<li>get_batch_tile_idx()
: <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemvBatchedStridedThreadblockDefaultSwizzle.html#abb9af4deb297039706283d3c5a8e8107">cutlass::gemm::threadblock::GemvBatchedStridedThreadblockDefaultSwizzle</a>
</li>
<li>get_cmd_line_argument()
: <a class="el" href="structcutlass_1_1CommandLine.html#a06962a53ee69752551c0353e1eb98d98">cutlass::CommandLine</a>
</li>
<li>get_cmd_line_argument_pairs()
: <a class="el" href="structcutlass_1_1CommandLine.html#a38f905a17e6c6e7bd2d1bea9e0c72088">cutlass::CommandLine</a>
</li>
<li>get_cmd_line_argument_ranges()
: <a class="el" href="structcutlass_1_1CommandLine.html#a935f23b162d87148cadb56f9a16e094e">cutlass::CommandLine</a>
</li>
<li>get_cmd_line_arguments()
: <a class="el" href="structcutlass_1_1CommandLine.html#a604c5d891f1328b071290d5341119c2c">cutlass::CommandLine</a>
</li>
<li>get_deleter()
: <a class="el" href="structcutlass_1_1device__memory_1_1allocation.html#a9f4ac6b947801d56521e8b6eefb72c2f">cutlass::device_memory::allocation&lt; T &gt;</a>
, <a class="el" href="classcutlass_1_1platform_1_1unique__ptr.html#aa427ab4ea4f2336ac6db28d53a4c11ac">cutlass::platform::unique_ptr&lt; T, Deleter &gt;</a>
</li>
<li>get_device_workspace_size()
: <a class="el" href="classcutlass_1_1library_1_1Operation.html#a7244b64a7e75ecc5b13eeecb77560b81">cutlass::library::Operation</a>
</li>
<li>get_grid_layout()
: <a class="el" href="structcutlass_1_1reduction_1_1DefaultBlockSwizzle.html#a70bdfa46a246b4fd6d000ffefe8778e4">cutlass::reduction::DefaultBlockSwizzle</a>
</li>
<li>get_grid_shape()
: <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmBatchedIdentityThreadblockSwizzle.html#ae95c8dc3f395280c6b78c4964a459910">cutlass::gemm::threadblock::GemmBatchedIdentityThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmHorizontalThreadblockSwizzle.html#adabab20237297aea8c5c0bf10659867a">cutlass::gemm::threadblock::GemmHorizontalThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmIdentityThreadblockSwizzle.html#a6175367cbcec704a58d440fa180acca9">cutlass::gemm::threadblock::GemmIdentityThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmSplitKHorizontalThreadblockSwizzle.html#a73ec37e7fff8b5bb165c6314561aedd0">cutlass::gemm::threadblock::GemmSplitKHorizontalThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmSplitKIdentityThreadblockSwizzle.html#ac23e3f04f7987bd43ba6e75875935dbe">cutlass::gemm::threadblock::GemmSplitKIdentityThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemvBatchedStridedThreadblockDefaultSwizzle.html#a66ce3d52667a0ae30d22ddeffd75ea65">cutlass::gemm::threadblock::GemvBatchedStridedThreadblockDefaultSwizzle</a>
</li>
<li>get_host_workspace_size()
: <a class="el" href="classcutlass_1_1library_1_1Operation.html#a5d66719a4503de4de907573e6e1df750">cutlass::library::Operation</a>
</li>
<li>get_lane_layout()
: <a class="el" href="structcutlass_1_1gemm_1_1warp_1_1MmaSimtPolicy.html#a71149619ab501844b9a50e38415057fa">cutlass::gemm::warp::MmaSimtPolicy&lt; WarpShape_, LaneLayout_, LaneMmaShape_ &gt;</a>
</li>
<li>get_mask()
: <a class="el" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a8a7ceae2b239a602be46efb20cf34d04">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator&lt; ThreadMap_, Element_, InterleavedK &gt;</a>
, <a class="el" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#acc5731288068b9da3eb6f63d63e86bec">cutlass::epilogue::threadblock::PredicatedTileIterator&lt; ThreadMap_, Element_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__da632779aba661c0f4cfaaa78126b771.html#aed620452104f231101309b5df7ef3738">cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__1790abaa54a01f277d75766d5882fec8.html#a4436d5a2d2a8ecdc9c1807818dbd2107">cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__7327fa15996bcb8502cdfcc192350fe1.html#a0bc8cfc65e89e57bf9146d48763dc7b5">cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen89c687c583745a73cb485041911a4c4e.html#ad59fc6d22ff05118cce8faea642cb3ca">cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemenab63a1e105bf37f6371516cb9e2c5a7a.html#a1ba5bb1d7fccb4ae7bf35eeac5f4459b">cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen784a0e9da3f55064c47e5613791f51f7.html#a9807f77a2fec69da63afd9c789123cd4">cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen9838736ad62fae54213fbaf722a989ab.html#aed9d7f02361544bb126e7673776b269f">cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen809793e785fb4211888c6b4e5dcfcb39.html#a68823be3fa87aeaf205f0acadfde9942">cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessType_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator2dThreadTile_3_01Shape___00_0165b39a630d10785a3558406f9adb99b9.html#a56503db6f71a1327f375bbbdefc69433">cutlass::transform::threadblock::PredicatedTileIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Transpose_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator2dThreadTile_3_01Shape___00_017a517f3c73efd795ab05059cc9b111e1.html#a73852507077f6058a1efe2bd85bb402d">cutlass::transform::threadblock::PredicatedTileIterator2dThreadTile&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, Transpose_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator2dThreadTile_3_01Shape___00_013671177d6219bfeb0e1b4dc4c1b5bf11.html#aea87c618a73e18e373dada2a1d33041c">cutlass::transform::threadblock::PredicatedTileIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Transpose_ &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a8a2ddd611de1585295a6f698a8a60022">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a02237c18b5f4662756ef7f1898a75a2d">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00e7c2c404e7aedfe60ad56bb5571306a1.html#a7ba9103031ca5d3e09867d057417594d">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::PitchLinear, AdvanceRank, ThreadMap_, AccessSize &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#a958f75bd15e03403c1517c767bc06b9e">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;</a>
, <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#a44b1d56e527e466f8406b19cd51f34a5">cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;</a>
</li>
<li>get_state()
: <a class="el" href="classcutlass_1_1Semaphore.html#a4ddbd78190c1342d2f964ad2ce18b59e">cutlass::Semaphore</a>
</li>
<li>get_threadblock_offset()
: <a class="el" href="structcutlass_1_1reduction_1_1DefaultBlockSwizzle.html#ac4e26c28b5b715340a399e86213ae4d1">cutlass::reduction::DefaultBlockSwizzle</a>
</li>
<li>get_tile_offset()
: <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmBatchedIdentityThreadblockSwizzle.html#a2cc792690e40cc36cfbb42f26c3ea2b8">cutlass::gemm::threadblock::GemmBatchedIdentityThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmHorizontalThreadblockSwizzle.html#a196c8afc17c2c7cbd820614a03d06928">cutlass::gemm::threadblock::GemmHorizontalThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmIdentityThreadblockSwizzle.html#a5259f478c5b7a1ba88a8a036ddb01da8">cutlass::gemm::threadblock::GemmIdentityThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmSplitKHorizontalThreadblockSwizzle.html#a32b3804f4c919c9320e29f291582fa66">cutlass::gemm::threadblock::GemmSplitKHorizontalThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmSplitKIdentityThreadblockSwizzle.html#a2447ddb5a74c693bd7fc0dccca1f0a33">cutlass::gemm::threadblock::GemmSplitKIdentityThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemvBatchedStridedThreadblockDefaultSwizzle.html#a57764c1f50acf18051053ad08833aad6">cutlass::gemm::threadblock::GemvBatchedStridedThreadblockDefaultSwizzle</a>
</li>
<li>get_tiled_shape()
: <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmBatchedIdentityThreadblockSwizzle.html#a8f2a25091658c44562ef129f745f67b4">cutlass::gemm::threadblock::GemmBatchedIdentityThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmHorizontalThreadblockSwizzle.html#a82794338c9536cda214d1356d6f48de6">cutlass::gemm::threadblock::GemmHorizontalThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmIdentityThreadblockSwizzle.html#aa3335372a11084045420b8191fc5b287">cutlass::gemm::threadblock::GemmIdentityThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmSplitKHorizontalThreadblockSwizzle.html#a58053e2cc92dc53d99612ff102bedb0c">cutlass::gemm::threadblock::GemmSplitKHorizontalThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemmSplitKIdentityThreadblockSwizzle.html#ab9e4678daa65c9ac9391d3010707d35c">cutlass::gemm::threadblock::GemmSplitKIdentityThreadblockSwizzle</a>
, <a class="el" href="structcutlass_1_1gemm_1_1threadblock_1_1GemvBatchedStridedThreadblockDefaultSwizzle.html#a335a95768ea3a82ed03cf4a1e3b95e31">cutlass::gemm::threadblock::GemvBatchedStridedThreadblockDefaultSwizzle</a>
</li>
<li>get_workspace_size()
: <a class="el" href="classcutlass_1_1gemm_1_1device_1_1Gemm.html#a0fec423a58e8de8ff7b015e5167ac614">cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, LayoutC_, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;</a>
, <a class="el" href="classcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layout4d0960ae6b1d1bf19e6239dbd002249c.html#a1469133c30fde6b28296e3ff6951e7a4">cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;</a>
, <a class="el" href="classcutlass_1_1gemm_1_1device_1_1GemmBatched.html#ac2009bb52372115624aa5c4f75b720e5">cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, LayoutC_, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;</a>
, <a class="el" href="classcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_0c9bb6f4463ab6085e6008b5d5ad6abfd.html#a3687659e826ba7f38bb060ad6020a739">cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;</a>
, <a class="el" href="classcutlass_1_1gemm_1_1device_1_1GemmComplex.html#a2ac229797cb1f22b641cd4f07997fea3">cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, LayoutC_, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;</a>
, <a class="el" href="classcutlass_1_1gemm_1_1device_1_1GemmComplex_3_01ElementA___00_01LayoutA___00_01ElementB___00_07c56401b4df75709ae636675d9980a9a.html#a75342fc4122c07d1382b31ee5f188210">cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;</a>
, <a class="el" href="classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel.html#ae8fb82c40078cf84c211f10f726caaf5">cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, LayoutC_, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;</a>
, <a class="el" href="classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01ElementBbe7c1f7154ad5b5bf9d4d28301e2b457.html#a4d9c70e23eef0a15d849b5b0ebadfcdd">cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;</a>
</li>
<li>good()
: <a class="el" href="classcutlass_1_1TensorRef.html#ac968ea9cb34fa99d29c64608c53bd4d4">cutlass::TensorRef&lt; Element_, Layout_ &gt;</a>
</li>
<li>grid_shape()
: <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#a696cecdb049ddb78b3d40530abbba1fb">cutlass::reduction::kernel::ReduceSplitK&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
