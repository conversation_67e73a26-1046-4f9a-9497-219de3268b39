<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: exceptions.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#namespaces">Namespaces</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">exceptions.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>C++ exception semantics for CUDA error codes.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;cuda_runtime.h&gt;</code><br />
<code>#include &lt;iosfwd&gt;</code><br />
<code>#include &lt;stdexcept&gt;</code><br />
<code>#include &quot;<a class="el" href="platform_8h_source.html">cutlass/platform/platform.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for exceptions.h:</div>
<div class="dyncontent">
<div class="center"><img src="exceptions_8h__incl.png" border="0" usemap="#exceptions_8h" alt=""/></div>
<map name="exceptions_8h" id="exceptions_8h">
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="exceptions_8h__dep__incl.png" border="0" usemap="#exceptions_8hdep" alt=""/></div>
<map name="exceptions_8hdep" id="exceptions_8hdep">
</map>
</div>
</div>
<p><a href="exceptions_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classcutlass_1_1cuda__exception.html">cutlass::cuda_exception</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">C++ exception wrapper for CUDA <code>cudaError_t</code>.  <a href="classcutlass_1_1cuda__exception.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespacecutlass"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html">cutlass</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:acd972e1b9d8a8a5093167fa0b98c5b60"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#acd972e1b9d8a8a5093167fa0b98c5b60">cutlass::operator&lt;&lt;</a> (std::ostream &amp;out, cudaError_t result)</td></tr>
<tr class="memdesc:acd972e1b9d8a8a5093167fa0b98c5b60"><td class="mdescLeft">&#160;</td><td class="mdescRight">Writes a cudaError_t to an output stream.  <a href="namespacecutlass.html#acd972e1b9d8a8a5093167fa0b98c5b60">More...</a><br /></td></tr>
<tr class="separator:acd972e1b9d8a8a5093167fa0b98c5b60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72666c1b605c515f2fd895c6fd0e8e09"><td class="memItemLeft" align="right" valign="top">std::ostream &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#a72666c1b605c515f2fd895c6fd0e8e09">cutlass::operator&lt;&lt;</a> (std::ostream &amp;out, cuda_exception const &amp;e)</td></tr>
<tr class="memdesc:a72666c1b605c515f2fd895c6fd0e8e09"><td class="mdescLeft">&#160;</td><td class="mdescRight">Writes a <a class="el" href="classcutlass_1_1cuda__exception.html" title="C++ exception wrapper for CUDA cudaError_t. ">cuda_exception</a> instance to an output stream.  <a href="namespacecutlass.html#a72666c1b605c515f2fd895c6fd0e8e09">More...</a><br /></td></tr>
<tr class="separator:a72666c1b605c515f2fd895c6fd0e8e09"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
