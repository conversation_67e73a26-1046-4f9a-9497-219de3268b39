<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: predicated_tile_iterator.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_05a6795d99d74f63b7300fc6eb9e55c2.html">threadblock</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">epilogue/threadblock/predicated_tile_iterator.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="epilogue_2threadblock_2predicated__tile__iterator_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="matrix__shape_8h.html">cutlass/matrix_shape.h</a>&quot;</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__ref_8h.html">cutlass/tensor_ref.h</a>&quot;</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="pitch__linear__thread__map_8h.html">cutlass/transform/pitch_linear_thread_map.h</a>&quot;</span></div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="output__tile__thread__map_8h.html">cutlass/epilogue/threadblock/output_tile_thread_map.h</a>&quot;</span></div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="keyword">namespace </span>threadblock {</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keyword">typename</span> ThreadMap_,       </div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="keyword">typename</span> Element_          </div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;&gt;</div><div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html">   65</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> {</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#abf4630b4ee3a1074de449c80cf89cba7">   67</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#abf4630b4ee3a1074de449c80cf89cba7">ThreadMap</a> = ThreadMap_;</div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ab63a8a4b7eef05d60729c45f43928b34">   68</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ab63a8a4b7eef05d60729c45f43928b34">Shape</a> = <span class="keyword">typename</span> ThreadMap::Shape;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div><div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#abc612400e34733a1b472e481a2293ade">   70</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#abc612400e34733a1b472e481a2293ade">Element</a> = Element_;</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a00c57a2cc53aa5aacd6672cf9af16e26">   72</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#abfaf98fde7c3a3b7cbd773498cf6c738">   73</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a53cca23a482d1e55ca3e21011a54ae79">   74</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a53cca23a482d1e55ca3e21011a54ae79">ConstTensorRef</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#ad3c5c9466713f62a5191e720827f34da">TensorRef::ConstTensorRef</a>;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">   76</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">Index</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html#aa49e242b14b4f482bc6bdd082acfb576">Layout::Index</a>;</div><div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a479672c177874980a3ccf436ae7946d5">   77</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a479672c177874980a3ccf436ae7946d5">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html#a4062a36ab044fdea058504ed52ee60b8">Layout::LongIndex</a>;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a487475c5bcda1d38d0d752f4b8c53d68">   78</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ab3ad40c4debee260a23d76194b114657">   80</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ab3ad40c4debee260a23d76194b114657">kElementsPerAccess</a> = ThreadMap::kElementsPerAccess;</div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ad6b1a44f14127ee55f70b8d2c043c67e">   81</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ad6b1a44f14127ee55f70b8d2c043c67e">kThreads</a> = ThreadMap::kThreads;</div><div class="line"><a name="l00082"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a7848f893107ec20d56abb46bc05e0e43">   82</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a7848f893107ec20d56abb46bc05e0e43">kIterations</a> = ThreadMap::Count::kTile;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>( ThreadMap::Iterations::kRow &gt; 0,<span class="stringliteral">&quot;ThreadMap::Iterations::kRow must be &gt; 0&quot;</span>);</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>( ThreadMap::Iterations::kGroup &gt; 0,<span class="stringliteral">&quot;ThreadMap::Iterations::kGroup must be &gt; 0&quot;</span>);</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>( ThreadMap::Iterations::kCluster &gt; 0,<span class="stringliteral">&quot;ThreadMap::Iterations::kCluster must be &gt; 0&quot;</span>);</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>( ThreadMap::Iterations::kColumn &gt; 0,<span class="stringliteral">&quot;ThreadMap::Iterations::kColumn must be &gt; 0&quot;</span>);</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a69151e6217b907fc20888e93f95cc333">Fragment</a> = Array&lt;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#abc612400e34733a1b472e481a2293ade">Element</a>, </div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;    ThreadMap::Iterations::kColumn * </div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    ThreadMap::Iterations::kRow * </div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;    ThreadMap::Iterations::kGroup * </div><div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a69151e6217b907fc20888e93f95cc333">   95</a></span>&#160;    ThreadMap::Iterations::kCluster * ThreadMap::kElementsPerAccess&gt;;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#afde3952d986cff98cf39872192b66a73">   98</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> = <a class="code" href="classcutlass_1_1AlignedArray.html">AlignedArray&lt;Element, ThreadMap::kElementsPerAccess&gt;</a>;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  <span class="comment">// Parameters struct</span></div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div><div class="line"><a name="l00104"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html">  104</a></span>&#160;  <span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html">Params</a> {</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;    <span class="comment">// Data members</span></div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a42d033e4b2de8a287affa5c25abb3f38">  110</a></span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">Index</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a42d033e4b2de8a287affa5c25abb3f38">stride</a>;               </div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div><div class="line"><a name="l00112"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#aca9106ffd4fe4e3d139cf01f3916bcba">  112</a></span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">Index</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#aca9106ffd4fe4e3d139cf01f3916bcba">increment_row</a>;        </div><div class="line"><a name="l00113"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a5234c0bbe10bc907f90776dbce50d504">  113</a></span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">Index</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a5234c0bbe10bc907f90776dbce50d504">increment_group</a>;      </div><div class="line"><a name="l00114"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#af3e71c49f5c4830451ba7a70b960b772">  114</a></span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">Index</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#af3e71c49f5c4830451ba7a70b960b772">increment_cluster</a>;    </div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div><div class="line"><a name="l00116"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a4ceaade8da07a3951a30c6d24b79f557">  116</a></span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">Index</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a4ceaade8da07a3951a30c6d24b79f557">advance_row</a>;          </div><div class="line"><a name="l00117"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a010385e2b1e39fb0a42ce65c68e07e8e">  117</a></span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">Index</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a010385e2b1e39fb0a42ce65c68e07e8e">advance_group</a>;        </div><div class="line"><a name="l00118"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a38a6dcfddaf9078334107eb8a38595fb">  118</a></span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">Index</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a38a6dcfddaf9078334107eb8a38595fb">advance_cluster</a>;      </div><div class="line"><a name="l00119"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a93f34cf9a98ab9bf6b2f7156848c9efd">  119</a></span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">Index</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a93f34cf9a98ab9bf6b2f7156848c9efd">advance_tile</a>;         </div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;    <span class="comment">// Methods</span></div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#ad5da25e1dd34da92acbb00b25f4be7f5">  126</a></span>&#160;    <a class="code" href="namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18d">Status</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#ad5da25e1dd34da92acbb00b25f4be7f5">initialize</a>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">Index</a> stride_) {</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;      </div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;      stride = stride_;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;      increment_row = stride * ThreadMap::Delta::kRow;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;      increment_group = stride * ThreadMap::Delta::kGroup</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;        - stride * ThreadMap::Delta::kRow * (ThreadMap::Iterations::kRow - 1);</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;      increment_cluster = stride * ThreadMap::Delta::kCluster</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;        - stride * ThreadMap::Delta::kGroup * (ThreadMap::Iterations::kGroup - 1)</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;        - stride * ThreadMap::Delta::kRow * (ThreadMap::Iterations::kRow - 1);</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;      advance_row = stride * ThreadMap::Shape::kRow;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;      advance_group = stride * (ThreadMap::Shape::kGroup - 1) * ThreadMap::Shape::kRow * ThreadMap::Count::kRow;</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;      </div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;      advance_cluster = </div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        stride * </div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        ThreadMap::Count::kGroup * ThreadMap::Shape::kGroup * ThreadMap::Count::kRow * ThreadMap::Shape::kRow;;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;      </div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;      advance_tile = </div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;        stride * </div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        ThreadMap::Shape::kGroup * </div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;        ThreadMap::Shape::kRow * </div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;        ThreadMap::Shape::kCluster * </div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;        ThreadMap::Shape::kTile;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;      <span class="keywordflow">return</span> <a class="code" href="namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18da8c632159fa131f09d04f94e3cbcd8782">Status::kSuccess</a>;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    }</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00158"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#af884dcf4ef98ad19a5e9e5af9dfa3e40">  158</a></span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#af884dcf4ef98ad19a5e9e5af9dfa3e40">Params</a>() {</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;      <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#ad5da25e1dd34da92acbb00b25f4be7f5">initialize</a>(0);</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;    }</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00163"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#adb1df805a2588de57fcc04dd41b1d76c">  163</a></span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#adb1df805a2588de57fcc04dd41b1d76c">Params</a>(<a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> <span class="keyword">const</span> &amp;layout) {</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;      <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#ad5da25e1dd34da92acbb00b25f4be7f5">initialize</a>(layout.<a class="code" href="classcutlass_1_1layout_1_1RowMajor.html#a0778f24212e546694887f308679426db">stride</a>(0) * int(<span class="keyword">sizeof</span>(<a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a>)) / <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ab3ad40c4debee260a23d76194b114657">kElementsPerAccess</a>);</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;    }</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;  };</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;</div><div class="line"><a name="l00170"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html">  170</a></span>&#160;  <span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html">Mask</a> {</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div><div class="line"><a name="l00172"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a87069316c04bf6da67a5e7b5259def57">  172</a></span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kCount = ThreadMap::Iterations::kColumn;</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div><div class="line"><a name="l00175"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a1a03001c853066670b8533eba6866b52">  175</a></span>&#160;    <span class="keywordtype">bool</span> predicates[kCount];</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    <span class="comment">// Mask</span></div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00181"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#aefa401d42f20dd3740d90a410bcedc83">  181</a></span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#aefa401d42f20dd3740d90a410bcedc83">Mask</a>() {</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;      enable();</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;    }</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;</div><div class="line"><a name="l00186"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a0fcbcea35583d096e4154209237ba217">  186</a></span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a0fcbcea35583d096e4154209237ba217">clear</a>() {</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; kCount; ++i) {</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;        predicates[i] = <span class="keyword">false</span>;</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;      }</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;    }</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;</div><div class="line"><a name="l00194"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a119c21a58cf4c627c9083412a44b9d2f">  194</a></span>&#160;    CUTLASS_DEVICE <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a119c21a58cf4c627c9083412a44b9d2f">enable</a>() {</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; kCount; ++i) {</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;        predicates[i] = <span class="keyword">true</span>;</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;      }</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;    }</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;  };</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;  <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html">Params</a> params_;</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;  uint8_t *byte_pointer_;</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;  <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html">Mask</a> mask_;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">Index</a> extent_row_;</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">Index</a> thread_start_row_;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;  <span class="keywordtype">int</span> state_[3];</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00240"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a0b73a5e03549ccf747bf5bf3c07f6f27">  240</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a0b73a5e03549ccf747bf5bf3c07f6f27">PredicatedTileIterator</a>(</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html">Params</a> <span class="keyword">const</span> &amp; params,</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;    Element *pointer,</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;    <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> extent,</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;    <span class="keywordtype">int</span> thread_idx,</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;    <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> threadblock_offset = <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a487475c5bcda1d38d0d752f4b8c53d68">TensorCoord</a>()</div><div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;  ):</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;    params_(params) {</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;    <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> thread_offset = ThreadMap::initial_offset(thread_idx) + threadblock_offset;</div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;    extent_row_ = extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>();</div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;    thread_start_row_ = thread_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>();</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;</div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;    <span class="comment">// Initialize predicates</span></div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> c = 0; c &lt; ThreadMap::Iterations::kColumn; ++c) {</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;</div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;      mask_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a1a03001c853066670b8533eba6866b52">predicates</a>[c] = ((thread_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>() </div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;        + ThreadMap::Delta::kColumn * c) &lt; extent.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>());</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;    }</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;    <span class="comment">// Initialize pointer</span></div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;    byte_pointer_ = <span class="keyword">reinterpret_cast&lt;</span>uint8_t *<span class="keyword">&gt;</span>(pointer) + </div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;      thread_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>() * params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a42d033e4b2de8a287affa5c25abb3f38">stride</a> + </div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;      thread_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>() * <span class="keyword">sizeof</span>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#afde3952d986cff98cf39872192b66a73">AccessType</a>) / kElementsPerAccess;</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;    <span class="comment">// Initialize internal state counter</span></div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;    state_[0] = state_[1] = state_[2] = 0;</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;  }</div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00273"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a879f53af759d71280cca8a8932002244">  273</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a879f53af759d71280cca8a8932002244">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a479672c177874980a3ccf436ae7946d5">LongIndex</a> pointer_offset) {</div><div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;    byte_pointer_ += pointer_offset * <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a> / 8;</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;  }</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;</div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00279"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a6994f682b05a9eac3e1e83e9717136d0">  279</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a6994f682b05a9eac3e1e83e9717136d0">load</a>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a69151e6217b907fc20888e93f95cc333">Fragment</a> &amp;frag) {</div><div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;</div><div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;    uint8_t *byte_pointer = byte_pointer_;</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;    <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;</div><div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> cluster = 0; cluster &lt; ThreadMap::Iterations::kCluster; ++cluster) {</div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;</div><div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> group = 0; group &lt; ThreadMap::Iterations::kGroup; ++group) {</div><div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;</div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;        <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> row = 0; row &lt; ThreadMap::Iterations::kRow; ++row) {</div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;</div><div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;          <span class="keywordtype">int</span> frag_row_idx = </div><div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;            (row + ThreadMap::Iterations::kRow * (group + ThreadMap::Iterations::kGroup * cluster));</div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;          <span class="keywordtype">int</span> row_offset = row * ThreadMap::Delta::kRow </div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;            + group * ThreadMap::Delta::kGroup </div><div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;            + cluster * ThreadMap::Delta::kCluster;</div><div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;</div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;          <span class="keywordtype">bool</span> row_guard = ((row_offset + thread_start_row_) &lt; extent_row_);</div><div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;</div><div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;          <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *memory_pointer = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *<span class="keyword">&gt;</span>(byte_pointer);</div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;          <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;          <span class="keywordflow">for</span> (<span class="keywordtype">int</span> column = 0; column &lt; ThreadMap::Iterations::kColumn; ++column) {</div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;</div><div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;            <span class="keywordtype">bool</span> guard = row_guard &amp;&amp; mask_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a1a03001c853066670b8533eba6866b52">predicates</a>[column];</div><div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;</div><div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;            <span class="keywordflow">if</span> (guard) {</div><div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;              frag_ptr[frag_row_idx * ThreadMap::Iterations::kColumn + column] = </div><div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;                memory_pointer[column * ThreadMap::Delta::kColumn / <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ab3ad40c4debee260a23d76194b114657">kElementsPerAccess</a>];</div><div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;            }</div><div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;          }</div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;</div><div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;          <span class="keywordflow">if</span> (row + 1 &lt; ThreadMap::Iterations::kRow) {</div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;            byte_pointer += params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#aca9106ffd4fe4e3d139cf01f3916bcba">increment_row</a>;</div><div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;          }</div><div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;        }</div><div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;</div><div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;        <span class="keywordflow">if</span> (group + 1 &lt; ThreadMap::Iterations::kGroup) {</div><div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;          byte_pointer += params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a5234c0bbe10bc907f90776dbce50d504">increment_group</a>;</div><div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;        }</div><div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;      }</div><div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;</div><div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;      <span class="keywordflow">if</span> (cluster + 1 &lt; ThreadMap::Iterations::kCluster) {</div><div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;        byte_pointer += params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#af3e71c49f5c4830451ba7a70b960b772">increment_cluster</a>;</div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;      }</div><div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;    }</div><div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;  }</div><div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;</div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00333"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#abf6174cc853f67c4c540757c599f6240">  333</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#abf6174cc853f67c4c540757c599f6240">store</a>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a69151e6217b907fc20888e93f95cc333">Fragment</a> <span class="keyword">const</span> &amp;frag) {</div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;    uint8_t *byte_pointer = byte_pointer_;</div><div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;    <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> <span class="keyword">const</span> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;</div><div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> cluster = 0; cluster &lt; ThreadMap::Iterations::kCluster; ++cluster) {</div><div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;</div><div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> group = 0; group &lt; ThreadMap::Iterations::kGroup; ++group) {</div><div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;</div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;        <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> row = 0; row &lt; ThreadMap::Iterations::kRow; ++row) {</div><div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;</div><div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;          <span class="keywordtype">int</span> frag_row_idx = </div><div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;            (row + ThreadMap::Iterations::kRow * (group + ThreadMap::Iterations::kGroup * cluster));</div><div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;          <span class="keywordtype">int</span> row_offset = row * ThreadMap::Delta::kRow </div><div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;            + group * ThreadMap::Delta::kGroup </div><div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;            + cluster * ThreadMap::Delta::kCluster;</div><div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;</div><div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;          <span class="keywordtype">bool</span> row_guard = ((row_offset + thread_start_row_) &lt; extent_row_);</div><div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;</div><div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;          <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *memory_pointer = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *<span class="keyword">&gt;</span>(byte_pointer);</div><div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;</div><div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;          <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;          <span class="keywordflow">for</span> (<span class="keywordtype">int</span> column = 0; column &lt; ThreadMap::Iterations::kColumn; ++column) {</div><div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;</div><div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;            <span class="keywordtype">bool</span> guard = row_guard &amp;&amp; mask_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a1a03001c853066670b8533eba6866b52">predicates</a>[column];</div><div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;</div><div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;            <span class="keywordflow">if</span> (guard) {</div><div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;              </div><div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;              memory_pointer[column * ThreadMap::Delta::kColumn / <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ab3ad40c4debee260a23d76194b114657">kElementsPerAccess</a>] =</div><div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;                frag_ptr[frag_row_idx * ThreadMap::Iterations::kColumn + column];</div><div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;            }</div><div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;          }</div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;</div><div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;          <span class="keywordflow">if</span> (row + 1 &lt; ThreadMap::Iterations::kRow) {</div><div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;            byte_pointer += params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#aca9106ffd4fe4e3d139cf01f3916bcba">increment_row</a>;</div><div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;          }</div><div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;        }</div><div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;</div><div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;        <span class="keywordflow">if</span> (group + 1 &lt; ThreadMap::Iterations::kGroup) {</div><div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;          byte_pointer += params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a5234c0bbe10bc907f90776dbce50d504">increment_group</a>;</div><div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;        }</div><div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;      }</div><div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;</div><div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;      <span class="keywordflow">if</span> (cluster + 1 &lt; ThreadMap::Iterations::kCluster) {</div><div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;        byte_pointer += params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#af3e71c49f5c4830451ba7a70b960b772">increment_cluster</a>;</div><div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;      }</div><div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;    }</div><div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;  }</div><div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;</div><div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00387"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a89c4e151beaab913b025276276fa7b3c">  387</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html">PredicatedTileIterator</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a89c4e151beaab913b025276276fa7b3c">operator++</a>() {</div><div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;</div><div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;    ++state_[0];</div><div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;    byte_pointer_ += params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a4ceaade8da07a3951a30c6d24b79f557">advance_row</a>;</div><div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;    thread_start_row_ += ThreadMap::Shape::kRow;</div><div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;    </div><div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;    <span class="keywordflow">if</span> (state_[0] == ThreadMap::Count::kRow) {</div><div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;</div><div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;      state_[0] = 0;</div><div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;      ++state_[1];</div><div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;      byte_pointer_ += params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a010385e2b1e39fb0a42ce65c68e07e8e">advance_group</a>;</div><div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;</div><div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;      thread_start_row_ += (ThreadMap::Shape::kGroup - 1) * </div><div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;        ThreadMap::Shape::kRow * ThreadMap::Count::kRow;</div><div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;</div><div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;      <span class="keywordflow">if</span> (state_[1] == ThreadMap::Count::kGroup) {</div><div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;</div><div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;        state_[1] = 0;</div><div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;        ++state_[2];</div><div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;        byte_pointer_ += params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a38a6dcfddaf9078334107eb8a38595fb">advance_cluster</a>;</div><div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;</div><div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;        thread_start_row_ += ThreadMap::Count::kGroup * </div><div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;          ThreadMap::Shape::kGroup * ThreadMap::Count::kRow * ThreadMap::Shape::kRow;</div><div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;</div><div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;        <span class="keywordflow">if</span> (state_[2] == ThreadMap::Count::kCluster) {</div><div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;          state_[2] = 0;</div><div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;          byte_pointer_ += params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a93f34cf9a98ab9bf6b2f7156848c9efd">advance_tile</a>;</div><div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;        }</div><div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;      }</div><div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;    }</div><div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;</div><div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;  }</div><div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;</div><div class="line"><a name="l00422"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a17a6ccbe829782c27e49f47922fce84a">  422</a></span>&#160;  CUTLASS_DEVICE <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a17a6ccbe829782c27e49f47922fce84a">clear_mask</a>() {</div><div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;    mask_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a0fcbcea35583d096e4154209237ba217">clear</a>();</div><div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;  }</div><div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;</div><div class="line"><a name="l00427"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a6a5a5722a000c06b769e58de1e1c3903">  427</a></span>&#160;  CUTLASS_DEVICE <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a6a5a5722a000c06b769e58de1e1c3903">enable_mask</a>() {</div><div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;    mask_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a119c21a58cf4c627c9083412a44b9d2f">enable</a>();</div><div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;  }</div><div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;</div><div class="line"><a name="l00432"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#acc5731288068b9da3eb6f63d63e86bec">  432</a></span>&#160;  CUTLASS_DEVICE <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#acc5731288068b9da3eb6f63d63e86bec">get_mask</a>(<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html">Mask</a> &amp;mask) {</div><div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;    <span class="keywordflow">return</span> mask_;</div><div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;  }</div><div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;</div><div class="line"><a name="l00437"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a338508178242ceae7a137e343c7e7630">  437</a></span>&#160;  CUTLASS_DEVICE <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a338508178242ceae7a137e343c7e7630">set_mask</a>(<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html">Mask</a> <span class="keyword">const</span> &amp;mask) {</div><div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;    mask_ = mask;</div><div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;  }</div><div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;};</div><div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;</div><div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;  <span class="keyword">typename</span> ThreadMap_,       </div><div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;  <span class="keyword">typename</span> Element_,         </div><div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;  <span class="keywordtype">int</span> InterleavedK           </div><div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;&gt;</div><div class="line"><a name="l00452"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html">  452</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html">InterleavedPredicatedTileIterator</a> {</div><div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00454"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#ac6ee81fec2a0f81a02b531f109d7ef46">  454</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#ac6ee81fec2a0f81a02b531f109d7ef46">ThreadMap</a> = ThreadMap_;</div><div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;</div><div class="line"><a name="l00456"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#adc0b65a9c683bd372fd07be5b3059688">  456</a></span>&#160;  <span class="keyword">using</span> Element = Element_;</div><div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;</div><div class="line"><a name="l00458"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a160a6628f34c37e13d13a9f87b0090a3">  458</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html">Layout</a> = <a class="code" href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html">layout::ColumnMajorInterleaved&lt;InterleavedK&gt;</a>;</div><div class="line"><a name="l00459"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#ac5801c0acbb4333f27dc51ed5f919966">  459</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>;</div><div class="line"><a name="l00460"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a5531982973996f04fb344d11e4e9d015">  460</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a5531982973996f04fb344d11e4e9d015">ConstTensorRef</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#ad3c5c9466713f62a5191e720827f34da">TensorRef::ConstTensorRef</a>;</div><div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;</div><div class="line"><a name="l00462"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#acc8e9e6194f2c47232f456b761349bf2">  462</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#acc8e9e6194f2c47232f456b761349bf2">Index</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html#aa49e242b14b4f482bc6bdd082acfb576">Layout::Index</a>;</div><div class="line"><a name="l00463"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#ab340cbc86fcd16ac7c380dac35f5c699">  463</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#ab340cbc86fcd16ac7c380dac35f5c699">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html#a4062a36ab044fdea058504ed52ee60b8">Layout::LongIndex</a>;</div><div class="line"><a name="l00464"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#ac24185f8939f93f7b66747efa907ea3d">  464</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">layout::PitchLinearCoord</a>;</div><div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;</div><div class="line"><a name="l00466"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a5787986f693785bc41cbb07ff1190ebc">  466</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = ThreadMap::kElementsPerAccess;</div><div class="line"><a name="l00467"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a2db384dfc9540ad07a8e4b21ff0799cb">  467</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kThreads = ThreadMap::kThreads;</div><div class="line"><a name="l00468"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a4e2a4d391df9a9fc709173cebde22501">  468</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = ThreadMap::Iterations::kCount;</div><div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;</div><div class="line"><a name="l00471"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a185d87d8c1aecb00eb09059068ccf1c6">  471</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a185d87d8c1aecb00eb09059068ccf1c6">Fragment</a> = Array&lt;Element, ThreadMap::kElementsPerAccess&gt;;</div><div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;</div><div class="line"><a name="l00474"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a2460e91b9c777313ec5c7295fde7d76f">  474</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> = <a class="code" href="classcutlass_1_1AlignedArray.html">AlignedArray&lt;Element, ThreadMap::kElementsPerAccess&gt;</a>;</div><div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;</div><div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00477"></a><span class="lineno">  477</span>&#160;  <span class="comment">// Parameters struct</span></div><div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160;</div><div class="line"><a name="l00480"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html">  480</a></span>&#160;  <span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html">Params</a> {</div><div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;</div><div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;    <span class="comment">// Data members</span></div><div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;</div><div class="line"><a name="l00486"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#ae3275575a9c4a54e8444b31bd3874996">  486</a></span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#acc8e9e6194f2c47232f456b761349bf2">Index</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#ae3275575a9c4a54e8444b31bd3874996">stride</a>;               </div><div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;</div><div class="line"><a name="l00488"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#a31c6472b5e890a37657d573646114156">  488</a></span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#acc8e9e6194f2c47232f456b761349bf2">Index</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#a31c6472b5e890a37657d573646114156">advance_row</a>;          </div><div class="line"><a name="l00489"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#a5593ba3ee47ba8bb6dc2b0b001b38824">  489</a></span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#acc8e9e6194f2c47232f456b761349bf2">Index</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#a5593ba3ee47ba8bb6dc2b0b001b38824">advance_column</a>;       </div><div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;</div><div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;    <span class="comment">// Methods</span></div><div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;</div><div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00496"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#a6f0ad92d44376e1bf61e7fb6932a3dfd">  496</a></span>&#160;    <a class="code" href="namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18d">Status</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#a6f0ad92d44376e1bf61e7fb6932a3dfd">initialize</a>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#acc8e9e6194f2c47232f456b761349bf2">Index</a> stride_) {</div><div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;      stride = stride_;</div><div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;</div><div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;      advance_row =</div><div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;          ThreadMap::Delta::kContiguous * <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a> / 8;</div><div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;</div><div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;      advance_column =</div><div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;          stride_ - ThreadMap::Iterations::kContiguous * kElementsPerAccess *</div><div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;                        <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a> * ThreadMap::kWarpSize / 8;</div><div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;</div><div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;      <span class="keywordflow">return</span> <a class="code" href="namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18da8c632159fa131f09d04f94e3cbcd8782">Status::kSuccess</a>;</div><div class="line"><a name="l00507"></a><span class="lineno">  507</span>&#160;    }</div><div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;</div><div class="line"><a name="l00509"></a><span class="lineno">  509</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00510"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#af64fa3173c9790da33060e7fe7574d7b">  510</a></span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#af64fa3173c9790da33060e7fe7574d7b">Params</a>() {</div><div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;      <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#ad5da25e1dd34da92acbb00b25f4be7f5">initialize</a>(0);</div><div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;    }</div><div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;</div><div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00515"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#acfdbad18358373f86ef8f2f3eae62a1f">  515</a></span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#acfdbad18358373f86ef8f2f3eae62a1f">Params</a>(<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html">Layout</a> <span class="keyword">const</span> &amp;layout) {</div><div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;</div><div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;      <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#ad5da25e1dd34da92acbb00b25f4be7f5">initialize</a>(layout.<a class="code" href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html#a5c651763f14af031cba81d5611fa4224">stride</a>(0) * int(<span class="keyword">sizeof</span>(<a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a>)) / <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ab3ad40c4debee260a23d76194b114657">kElementsPerAccess</a>);</div><div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;    }</div><div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;  };</div><div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;</div><div class="line"><a name="l00522"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html">  522</a></span>&#160;  <span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html">Mask</a> {</div><div class="line"><a name="l00523"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#ad15acdea319b1a73f3e2b0c9f4cda448">  523</a></span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kCount = (ThreadMap::Iterations::kContiguous &lt; 8)</div><div class="line"><a name="l00524"></a><span class="lineno">  524</span>&#160;                                  ? 8</div><div class="line"><a name="l00525"></a><span class="lineno">  525</span>&#160;                                  : ThreadMap::Iterations::kContiguous;</div><div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;</div><div class="line"><a name="l00528"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#a7491a28ffa24251ca9b1999392c443d2">  528</a></span>&#160;    <span class="keywordtype">bool</span> predicates[kCount];</div><div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;</div><div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;    <span class="comment">// Mask</span></div><div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00534"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#a1e08665b2b5cb30736b03e69ec215298">  534</a></span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#a1e08665b2b5cb30736b03e69ec215298">Mask</a>() {</div><div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;      enable();</div><div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;    }</div><div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160;</div><div class="line"><a name="l00539"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#aed4717037e76148efbb7bb68d6c4e509">  539</a></span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#aed4717037e76148efbb7bb68d6c4e509">clear</a>() {</div><div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00541"></a><span class="lineno">  541</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; kCount; ++i) {</div><div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;        predicates[i] = <span class="keyword">false</span>;</div><div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;      }</div><div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160;    }</div><div class="line"><a name="l00545"></a><span class="lineno">  545</span>&#160;</div><div class="line"><a name="l00547"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#acbd971f79b973257f09bacaa265f0520">  547</a></span>&#160;    CUTLASS_DEVICE <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#acbd971f79b973257f09bacaa265f0520">enable</a>() {</div><div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; kCount; ++i) {</div><div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160;        predicates[i] = <span class="keyword">true</span>;</div><div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;      }</div><div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;    }</div><div class="line"><a name="l00553"></a><span class="lineno">  553</span>&#160;  };</div><div class="line"><a name="l00554"></a><span class="lineno">  554</span>&#160;</div><div class="line"><a name="l00555"></a><span class="lineno">  555</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00556"></a><span class="lineno">  556</span>&#160;</div><div class="line"><a name="l00557"></a><span class="lineno">  557</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;</div><div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160;  <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html">Params</a> params_;</div><div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160;</div><div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160;  uint8_t *byte_pointer_;</div><div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;</div><div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;  <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html">Mask</a> mask_;</div><div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;</div><div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#acc8e9e6194f2c47232f456b761349bf2">Index</a> extent_col_;</div><div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;</div><div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#acc8e9e6194f2c47232f456b761349bf2">Index</a> thread_start_col_;</div><div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;</div><div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;  <span class="keywordtype">int</span> iteration_contiguous_;</div><div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;</div><div class="line"><a name="l00580"></a><span class="lineno">  580</span>&#160;  <span class="keywordtype">int</span> iteration_strided_;</div><div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;</div><div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00583"></a><span class="lineno">  583</span>&#160;</div><div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00586"></a><span class="lineno">  586</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;</div><div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;</div><div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00592"></a><span class="lineno">  592</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160;</div><div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00596"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#aab0960ebd371ed02c4c7c5f8e2c2caf5">  596</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#aab0960ebd371ed02c4c7c5f8e2c2caf5">InterleavedPredicatedTileIterator</a>(</div><div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html">Params</a> <span class="keyword">const</span> &amp; params,</div><div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160;    Element *pointer,</div><div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> extent,</div><div class="line"><a name="l00600"></a><span class="lineno">  600</span>&#160;    <span class="keywordtype">int</span> thread_idx,</div><div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> threadblock_offset</div><div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;  ):</div><div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160;    params_(params) {</div><div class="line"><a name="l00604"></a><span class="lineno">  604</span>&#160;    <a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">TensorCoord</a> thread_offset = ThreadMap::initial_offset(thread_idx) +</div><div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;                                <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a487475c5bcda1d38d0d752f4b8c53d68">TensorCoord</a>(threadblock_offset.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() * InterleavedK,</div><div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160;                                 threadblock_offset.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>() / InterleavedK);</div><div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;</div><div class="line"><a name="l00608"></a><span class="lineno">  608</span>&#160;    extent_col_ = extent.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>() / InterleavedK;</div><div class="line"><a name="l00609"></a><span class="lineno">  609</span>&#160;    thread_start_col_ = thread_offset.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>();</div><div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;</div><div class="line"><a name="l00611"></a><span class="lineno">  611</span>&#160;    <span class="comment">// Initialize predicates</span></div><div class="line"><a name="l00612"></a><span class="lineno">  612</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00613"></a><span class="lineno">  613</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> c = 0; c &lt; ThreadMap::Iterations::kContiguous; ++c) {</div><div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;      mask_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#a7491a28ffa24251ca9b1999392c443d2">predicates</a>[c] =</div><div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;          ((thread_offset.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() + ThreadMap::Delta::kContiguous * c) &lt;</div><div class="line"><a name="l00616"></a><span class="lineno">  616</span>&#160;           (extent.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() * InterleavedK));</div><div class="line"><a name="l00617"></a><span class="lineno">  617</span>&#160;    }</div><div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160;</div><div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;    <span class="comment">// Initialize pointer</span></div><div class="line"><a name="l00620"></a><span class="lineno">  620</span>&#160;    byte_pointer_ = <span class="keyword">reinterpret_cast&lt;</span>uint8_t *<span class="keyword">&gt;</span>(pointer) + </div><div class="line"><a name="l00621"></a><span class="lineno">  621</span>&#160;      thread_offset.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">strided</a>() * params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#ae3275575a9c4a54e8444b31bd3874996">stride</a> + </div><div class="line"><a name="l00622"></a><span class="lineno">  622</span>&#160;      thread_offset.<a class="code" href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">contiguous</a>() * <span class="keyword">sizeof</span>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#afde3952d986cff98cf39872192b66a73">AccessType</a>) / kElementsPerAccess;</div><div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;</div><div class="line"><a name="l00624"></a><span class="lineno">  624</span>&#160;    <span class="comment">// Initialize internal state counter</span></div><div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;    iteration_contiguous_ = iteration_strided_ = 0;</div><div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;  }</div><div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160;</div><div class="line"><a name="l00629"></a><span class="lineno">  629</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00630"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a12109679ba6ff108d8b4f633e60b9f5b">  630</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a12109679ba6ff108d8b4f633e60b9f5b">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#ab340cbc86fcd16ac7c380dac35f5c699">LongIndex</a> pointer_offset) {</div><div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;    byte_pointer_ += pointer_offset * <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a> / 8;</div><div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;  }</div><div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160;</div><div class="line"><a name="l00635"></a><span class="lineno">  635</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00636"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#adb3577ad7dba393626952f76c4aae465">  636</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#adb3577ad7dba393626952f76c4aae465">load</a>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a185d87d8c1aecb00eb09059068ccf1c6">Fragment</a> &amp;frag) {</div><div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;    uint8_t *byte_pointer = byte_pointer_;</div><div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;    <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160;    <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *memory_pointer = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *<span class="keyword">&gt;</span>(byte_pointer);</div><div class="line"><a name="l00640"></a><span class="lineno">  640</span>&#160;</div><div class="line"><a name="l00641"></a><span class="lineno">  641</span>&#160;    <span class="keywordtype">int</span> col_offset = iteration_strided_ * ThreadMap::Delta::kStrided;</div><div class="line"><a name="l00642"></a><span class="lineno">  642</span>&#160;</div><div class="line"><a name="l00643"></a><span class="lineno">  643</span>&#160;    <span class="keywordtype">bool</span> col_guard = ((thread_start_col_ + col_offset) &lt; extent_col_);</div><div class="line"><a name="l00644"></a><span class="lineno">  644</span>&#160;</div><div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;    <span class="keywordtype">bool</span> guard = col_guard &amp;&amp; mask_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#a7491a28ffa24251ca9b1999392c443d2">predicates</a>[iteration_contiguous_];</div><div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;</div><div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160;    <span class="keywordflow">if</span> (guard) {</div><div class="line"><a name="l00648"></a><span class="lineno">  648</span>&#160;      *frag_ptr = *memory_pointer;</div><div class="line"><a name="l00649"></a><span class="lineno">  649</span>&#160;    }</div><div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;  }</div><div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;</div><div class="line"><a name="l00653"></a><span class="lineno">  653</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00654"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a74ec605bd7b1dae43050309176ec85ba">  654</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a74ec605bd7b1dae43050309176ec85ba">store</a>(<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a185d87d8c1aecb00eb09059068ccf1c6">Fragment</a> <span class="keyword">const</span> &amp;frag) {</div><div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160;    uint8_t *byte_pointer = byte_pointer_;</div><div class="line"><a name="l00656"></a><span class="lineno">  656</span>&#160;    <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> <span class="keyword">const</span> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00657"></a><span class="lineno">  657</span>&#160;    <a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *memory_pointer = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1AlignedArray.html">AccessType</a> *<span class="keyword">&gt;</span>(byte_pointer);</div><div class="line"><a name="l00658"></a><span class="lineno">  658</span>&#160;</div><div class="line"><a name="l00659"></a><span class="lineno">  659</span>&#160;    <span class="keywordtype">int</span> col_offset = iteration_strided_ * ThreadMap::Delta::kStrided;</div><div class="line"><a name="l00660"></a><span class="lineno">  660</span>&#160;</div><div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160;    <span class="keywordtype">bool</span> col_guard = ((thread_start_col_ + col_offset) &lt; extent_col_);</div><div class="line"><a name="l00662"></a><span class="lineno">  662</span>&#160;</div><div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;    <span class="keywordtype">bool</span> guard = col_guard &amp;&amp; mask_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#a7491a28ffa24251ca9b1999392c443d2">predicates</a>[iteration_contiguous_];</div><div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;</div><div class="line"><a name="l00665"></a><span class="lineno">  665</span>&#160;    <span class="keywordflow">if</span> (guard) {</div><div class="line"><a name="l00666"></a><span class="lineno">  666</span>&#160;      *memory_pointer = *frag_ptr;</div><div class="line"><a name="l00667"></a><span class="lineno">  667</span>&#160;    }</div><div class="line"><a name="l00668"></a><span class="lineno">  668</span>&#160;  }</div><div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;</div><div class="line"><a name="l00671"></a><span class="lineno">  671</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00672"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a09320ba944aafa1fc753edf62b1c562c">  672</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a09320ba944aafa1fc753edf62b1c562c">set_iteration_index</a>(<span class="keywordtype">int</span> iteration) {</div><div class="line"><a name="l00673"></a><span class="lineno">  673</span>&#160;    iteration_contiguous_ = iteration % ThreadMap::Iterations::kContiguous;</div><div class="line"><a name="l00674"></a><span class="lineno">  674</span>&#160;    iteration_strided_ = iteration / ThreadMap::Iterations::kContiguous;</div><div class="line"><a name="l00675"></a><span class="lineno">  675</span>&#160;  }</div><div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;</div><div class="line"><a name="l00678"></a><span class="lineno">  678</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00679"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#aaf23d79b0bdc047bdab776370c916065">  679</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html">InterleavedPredicatedTileIterator</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#aaf23d79b0bdc047bdab776370c916065">operator++</a>() {</div><div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;</div><div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;    ++iteration_contiguous_;</div><div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;    byte_pointer_ += params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#a31c6472b5e890a37657d573646114156">advance_row</a>;</div><div class="line"><a name="l00683"></a><span class="lineno">  683</span>&#160;</div><div class="line"><a name="l00684"></a><span class="lineno">  684</span>&#160;    <span class="keywordflow">if</span> (iteration_contiguous_ == ThreadMap::Iterations::kContiguous) {</div><div class="line"><a name="l00685"></a><span class="lineno">  685</span>&#160;</div><div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;      iteration_contiguous_ = 0;</div><div class="line"><a name="l00687"></a><span class="lineno">  687</span>&#160;      ++iteration_strided_;</div><div class="line"><a name="l00688"></a><span class="lineno">  688</span>&#160;      byte_pointer_ += params_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#a5593ba3ee47ba8bb6dc2b0b001b38824">advance_column</a>;</div><div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;</div><div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;      <span class="keywordflow">if</span> (iteration_strided_ == ThreadMap::Iterations::kStrided) {</div><div class="line"><a name="l00691"></a><span class="lineno">  691</span>&#160;        iteration_strided_ = 0;</div><div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;      }</div><div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;    }</div><div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;</div><div class="line"><a name="l00695"></a><span class="lineno">  695</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00696"></a><span class="lineno">  696</span>&#160;  }</div><div class="line"><a name="l00697"></a><span class="lineno">  697</span>&#160;</div><div class="line"><a name="l00699"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#ab478d83e7b15b9eca8f3f281072cba38">  699</a></span>&#160;  CUTLASS_DEVICE <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#ab478d83e7b15b9eca8f3f281072cba38">clear_mask</a>() {</div><div class="line"><a name="l00700"></a><span class="lineno">  700</span>&#160;    mask_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#aed4717037e76148efbb7bb68d6c4e509">clear</a>();</div><div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;  }</div><div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160;</div><div class="line"><a name="l00704"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a4d9f5f0439afd4f459ed22c2b0db9070">  704</a></span>&#160;  CUTLASS_DEVICE <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a4d9f5f0439afd4f459ed22c2b0db9070">enable_mask</a>() {</div><div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;    mask_.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#acbd971f79b973257f09bacaa265f0520">enable</a>();</div><div class="line"><a name="l00706"></a><span class="lineno">  706</span>&#160;  }</div><div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160;</div><div class="line"><a name="l00709"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a8a7ceae2b239a602be46efb20cf34d04">  709</a></span>&#160;  CUTLASS_DEVICE <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a8a7ceae2b239a602be46efb20cf34d04">get_mask</a>(<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html">Mask</a> &amp;mask) {</div><div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;    <span class="keywordflow">return</span> mask_;</div><div class="line"><a name="l00711"></a><span class="lineno">  711</span>&#160;  }</div><div class="line"><a name="l00712"></a><span class="lineno">  712</span>&#160;</div><div class="line"><a name="l00714"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#aba9b6f085423136bf3cdd292ded36727">  714</a></span>&#160;  CUTLASS_DEVICE <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#aba9b6f085423136bf3cdd292ded36727">set_mask</a>(<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html">Mask</a> <span class="keyword">const</span> &amp;mask) {</div><div class="line"><a name="l00715"></a><span class="lineno">  715</span>&#160;    mask_ = mask;</div><div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;  }</div><div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160;};</div><div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;</div><div class="line"><a name="l00720"></a><span class="lineno">  720</span>&#160;</div><div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;} <span class="comment">// namespace threadblock</span></div><div class="line"><a name="l00722"></a><span class="lineno">  722</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00723"></a><span class="lineno">  723</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;</div><div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask_html_a1a03001c853066670b8533eba6866b52"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a1a03001c853066670b8533eba6866b52">cutlass::epilogue::threadblock::PredicatedTileIterator::Mask::predicates</a></div><div class="ttdeci">bool predicates[kCount]</div><div class="ttdoc">Predicate state. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:175</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html_a4062a36ab044fdea058504ed52ee60b8"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html#a4062a36ab044fdea058504ed52ee60b8">cutlass::layout::RowMajor::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:62</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_ab3ad40c4debee260a23d76194b114657"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ab3ad40c4debee260a23d76194b114657">cutlass::epilogue::threadblock::PredicatedTileIterator::kElementsPerAccess</a></div><div class="ttdeci">static int const kElementsPerAccess</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:80</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask_html_a119c21a58cf4c627c9083412a44b9d2f"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a119c21a58cf4c627c9083412a44b9d2f">cutlass::epilogue::threadblock::PredicatedTileIterator::Mask::enable</a></div><div class="ttdeci">CUTLASS_DEVICE void enable()</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:194</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_afbdcc5ca5b91f11f29046667b0bfde7b"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">cutlass::MatrixCoord::column</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; column() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:85</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params_html_a4ceaade8da07a3951a30c6d24b79f557"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a4ceaade8da07a3951a30c6d24b79f557">cutlass::epilogue::threadblock::PredicatedTileIterator::Params::advance_row</a></div><div class="ttdeci">Index advance_row</div><div class="ttdoc">amount to add to move to the next &amp;#39;row&amp;#39; position </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:116</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_a6994f682b05a9eac3e1e83e9717136d0"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a6994f682b05a9eac3e1e83e9717136d0">cutlass::epilogue::threadblock::PredicatedTileIterator::load</a></div><div class="ttdeci">CUTLASS_DEVICE void load(Fragment &amp;frag)</div><div class="ttdoc">Loads a fragment from memory. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:279</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_abc612400e34733a1b472e481a2293ade"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#abc612400e34733a1b472e481a2293ade">cutlass::epilogue::threadblock::PredicatedTileIterator::Element</a></div><div class="ttdeci">Element_ Element</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:70</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearCoord_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearCoord.html">cutlass::layout::PitchLinearCoord</a></div><div class="ttdoc">Coordinate in pitch-linear space. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:52</div></div>
<div class="ttc" id="tensor__ref_8h_html"><div class="ttname"><a href="tensor__ref_8h.html">tensor_ref.h</a></div><div class="ttdoc">Defines a structure containing strides, bounds, and a pointer to tensor data. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_afde3952d986cff98cf39872192b66a73"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#afde3952d986cff98cf39872192b66a73">cutlass::epilogue::threadblock::PredicatedTileIterator::AccessType</a></div><div class="ttdeci">AlignedArray&lt; Element, ThreadMap::kElementsPerAccess &gt; AccessType</div><div class="ttdoc">Memory access size. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:98</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params_html_a6f0ad92d44376e1bf61e7fb6932a3dfd"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#a6f0ad92d44376e1bf61e7fb6932a3dfd">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Params::initialize</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Status initialize(Index stride_)</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:496</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask_html_aed4717037e76148efbb7bb68d6c4e509"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#aed4717037e76148efbb7bb68d6c4e509">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Mask::clear</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void clear()</div><div class="ttdoc">CUTLASS_HOST_DEVICE enables all accesses guarded by mask. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:539</div></div>
<div class="ttc" id="pitch__linear__thread__map_8h_html"><div class="ttname"><a href="pitch__linear__thread__map_8h.html">pitch_linear_thread_map.h</a></div><div class="ttdoc">Templates implementing how threads are mapped to a given tile. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_acc5731288068b9da3eb6f63d63e86bec"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#acc5731288068b9da3eb6f63d63e86bec">cutlass::epilogue::threadblock::PredicatedTileIterator::get_mask</a></div><div class="ttdeci">CUTLASS_DEVICE void get_mask(Mask &amp;mask)</div><div class="ttdoc">Sets the mask. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:432</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_a185d87d8c1aecb00eb09059068ccf1c6"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a185d87d8c1aecb00eb09059068ccf1c6">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Fragment</a></div><div class="ttdeci">Array&lt; Element, ThreadMap::kElementsPerAccess &gt; Fragment</div><div class="ttdoc">Fragment object. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:471</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_ac6ee81fec2a0f81a02b531f109d7ef46"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#ac6ee81fec2a0f81a02b531f109d7ef46">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::ThreadMap</a></div><div class="ttdeci">ThreadMap_ ThreadMap</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:454</div></div>
<div class="ttc" id="classcutlass_1_1AlignedArray_html"><div class="ttname"><a href="classcutlass_1_1AlignedArray.html">cutlass::AlignedArray</a></div><div class="ttdoc">Aligned array type. </div><div class="ttdef"><b>Definition:</b> array.h:511</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html">cutlass::epilogue::threadblock::PredicatedTileIterator::Mask</a></div><div class="ttdoc">Mask object. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:170</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_a0580610f28427e376b24b71f67602d03"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">cutlass::MatrixCoord::row</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; row() const </div><div class="ttdoc">Returns the row of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:77</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask_html_a7491a28ffa24251ca9b1999392c443d2"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#a7491a28ffa24251ca9b1999392c443d2">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Mask::predicates</a></div><div class="ttdeci">bool predicates[kCount]</div><div class="ttdoc">Predicate state. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:528</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_a53cca23a482d1e55ca3e21011a54ae79"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a53cca23a482d1e55ca3e21011a54ae79">cutlass::epilogue::threadblock::PredicatedTileIterator::ConstTensorRef</a></div><div class="ttdeci">typename TensorRef::ConstTensorRef ConstTensorRef</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:74</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask_html_a1e08665b2b5cb30736b03e69ec215298"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#a1e08665b2b5cb30736b03e69ec215298">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Mask::Mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Mask()</div><div class="ttdoc">Efficiently disables all accesses guarded by mask. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:534</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorInterleaved_html_a5c651763f14af031cba81d5611fa4224"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html#a5c651763f14af031cba81d5611fa4224">cutlass::layout::ColumnMajorInterleaved::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:418</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html_a0778f24212e546694887f308679426db"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html#a0778f24212e546694887f308679426db">cutlass::layout::RowMajor::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:112</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_a69151e6217b907fc20888e93f95cc333"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a69151e6217b907fc20888e93f95cc333">cutlass::epilogue::threadblock::PredicatedTileIterator::Fragment</a></div><div class="ttdeci">Array&lt; Element, ThreadMap::Iterations::kColumn *ThreadMap::Iterations::kRow *ThreadMap::Iterations::kGroup *ThreadMap::Iterations::kCluster *ThreadMap::kElementsPerAccess &gt; Fragment</div><div class="ttdoc">Fragment object. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:95</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Params</a></div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:480</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_abf6174cc853f67c4c540757c599f6240"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#abf6174cc853f67c4c540757c599f6240">cutlass::epilogue::threadblock::PredicatedTileIterator::store</a></div><div class="ttdeci">CUTLASS_DEVICE void store(Fragment const &amp;frag)</div><div class="ttdoc">Stores a fragment to memory. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:333</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_a479672c177874980a3ccf436ae7946d5"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a479672c177874980a3ccf436ae7946d5">cutlass::epilogue::threadblock::PredicatedTileIterator::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:77</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ad3c5c9466713f62a5191e720827f34da"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ad3c5c9466713f62a5191e720827f34da">cutlass::TensorRef&lt; Element, Layout &gt;::ConstTensorRef</a></div><div class="ttdeci">TensorRef&lt; typename platform::remove_const&lt; Element &gt;::type const, Layout &gt; ConstTensorRef</div><div class="ttdoc">TensorRef to constant data. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:179</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_adb3577ad7dba393626952f76c4aae465"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#adb3577ad7dba393626952f76c4aae465">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::load</a></div><div class="ttdeci">CUTLASS_DEVICE void load(Fragment &amp;frag)</div><div class="ttdoc">Loads a fragment from memory. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:636</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_aab0960ebd371ed02c4c7c5f8e2c2caf5"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#aab0960ebd371ed02c4c7c5f8e2c2caf5">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::InterleavedPredicatedTileIterator</a></div><div class="ttdeci">CUTLASS_DEVICE InterleavedPredicatedTileIterator(Params const &amp;params, Element *pointer, TensorCoord extent, int thread_idx, TensorCoord threadblock_offset)</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:596</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_acc8e9e6194f2c47232f456b761349bf2"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#acc8e9e6194f2c47232f456b761349bf2">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:462</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html_aa49e242b14b4f482bc6bdd082acfb576"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html#aa49e242b14b4f482bc6bdd082acfb576">cutlass::layout::RowMajor::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:59</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params_html_a38a6dcfddaf9078334107eb8a38595fb"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a38a6dcfddaf9078334107eb8a38595fb">cutlass::epilogue::threadblock::PredicatedTileIterator::Params::advance_cluster</a></div><div class="ttdeci">Index advance_cluster</div><div class="ttdoc">amount to add to move to the next &amp;#39;cluster&amp;#39; position </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:118</div></div>
<div class="ttc" id="matrix__shape_8h_html"><div class="ttname"><a href="matrix__shape_8h.html">matrix_shape.h</a></div><div class="ttdoc">Defines a Shape template for matrix tiles. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_a74ec605bd7b1dae43050309176ec85ba"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a74ec605bd7b1dae43050309176ec85ba">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::store</a></div><div class="ttdeci">CUTLASS_DEVICE void store(Fragment const &amp;frag)</div><div class="ttdoc">Stores a fragment to memory. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:654</div></div>
<div class="ttc" id="structcutlass_1_1sizeof__bits_html"><div class="ttname"><a href="structcutlass_1_1sizeof__bits.html">cutlass::sizeof_bits</a></div><div class="ttdoc">Defines the size of an element in bits. </div><div class="ttdef"><b>Definition:</b> numeric_types.h:42</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params_html_a31c6472b5e890a37657d573646114156"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#a31c6472b5e890a37657d573646114156">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Params::advance_row</a></div><div class="ttdeci">Index advance_row</div><div class="ttdoc">amount to add to move to the next &amp;#39;row&amp;#39; position </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:488</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_ab478d83e7b15b9eca8f3f281072cba38"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#ab478d83e7b15b9eca8f3f281072cba38">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::clear_mask</a></div><div class="ttdeci">CUTLASS_DEVICE void clear_mask()</div><div class="ttdoc">Efficiently enables all accesses guarded by mask. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:699</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html"><div class="ttname"><a href="classcutlass_1_1TensorRef.html">cutlass::TensorRef&lt; Element, Layout &gt;</a></div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_abf4630b4ee3a1074de449c80cf89cba7"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#abf4630b4ee3a1074de449c80cf89cba7">cutlass::epilogue::threadblock::PredicatedTileIterator::ThreadMap</a></div><div class="ttdeci">ThreadMap_ ThreadMap</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:67</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Mask</a></div><div class="ttdoc">Mask object. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:522</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params_html_af64fa3173c9790da33060e7fe7574d7b"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#af64fa3173c9790da33060e7fe7574d7b">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params()</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:510</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_a0b73a5e03549ccf747bf5bf3c07f6f27"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a0b73a5e03549ccf747bf5bf3c07f6f27">cutlass::epilogue::threadblock::PredicatedTileIterator::PredicatedTileIterator</a></div><div class="ttdeci">CUTLASS_DEVICE PredicatedTileIterator(Params const &amp;params, Element *pointer, TensorCoord extent, int thread_idx, TensorCoord threadblock_offset=TensorCoord())</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:240</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearCoord_html_adb31bc9b8cf49dfff64245b70a850834"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearCoord.html#adb31bc9b8cf49dfff64245b70a850834">cutlass::layout::PitchLinearCoord::contiguous</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; contiguous() const </div><div class="ttdoc">Returns the contiguous dimension. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:89</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator</a></div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:452</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_a5531982973996f04fb344d11e4e9d015"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a5531982973996f04fb344d11e4e9d015">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::ConstTensorRef</a></div><div class="ttdeci">typename TensorRef::ConstTensorRef ConstTensorRef</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:460</div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_a09320ba944aafa1fc753edf62b1c562c"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a09320ba944aafa1fc753edf62b1c562c">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::set_iteration_index</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void set_iteration_index(int iteration)</div><div class="ttdoc">Overrides the internal iteration index. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:672</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params_html_a42d033e4b2de8a287affa5c25abb3f38"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a42d033e4b2de8a287affa5c25abb3f38">cutlass::epilogue::threadblock::PredicatedTileIterator::Params::stride</a></div><div class="ttdeci">Index stride</div><div class="ttdoc">stride in bytes between rows </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:110</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_a89c4e151beaab913b025276276fa7b3c"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a89c4e151beaab913b025276276fa7b3c">cutlass::epilogue::threadblock::PredicatedTileIterator::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE PredicatedTileIterator &amp; operator++()</div><div class="ttdoc">Advances to the next position to load or store. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:387</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params_html_adb1df805a2588de57fcc04dd41b1d76c"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#adb1df805a2588de57fcc04dd41b1d76c">cutlass::epilogue::threadblock::PredicatedTileIterator::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params(Layout const &amp;layout)</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:163</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params_html_ae3275575a9c4a54e8444b31bd3874996"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#ae3275575a9c4a54e8444b31bd3874996">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Params::stride</a></div><div class="ttdeci">Index stride</div><div class="ttdoc">stride in bytes between columns </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:486</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params_html_a5593ba3ee47ba8bb6dc2b0b001b38824"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#a5593ba3ee47ba8bb6dc2b0b001b38824">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Params::advance_column</a></div><div class="ttdeci">Index advance_column</div><div class="ttdoc">amount to add to move to the next &amp;#39;column&amp;#39; position </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:489</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html">cutlass::epilogue::threadblock::PredicatedTileIterator::Params</a></div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:104</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_a7848f893107ec20d56abb46bc05e0e43"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a7848f893107ec20d56abb46bc05e0e43">cutlass::epilogue::threadblock::PredicatedTileIterator::kIterations</a></div><div class="ttdeci">static int const kIterations</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:82</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params_html_a93f34cf9a98ab9bf6b2f7156848c9efd"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a93f34cf9a98ab9bf6b2f7156848c9efd">cutlass::epilogue::threadblock::PredicatedTileIterator::Params::advance_tile</a></div><div class="ttdeci">Index advance_tile</div><div class="ttdoc">amount to add to move to the next &amp;#39;tile&amp;#39; </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:119</div></div>
<div class="ttc" id="output__tile__thread__map_8h_html"><div class="ttname"><a href="output__tile__thread__map_8h.html">output_tile_thread_map.h</a></div><div class="ttdoc">Metaprogram for determining the mapping of output elements to threads for epilogue tiles...</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_aaf23d79b0bdc047bdab776370c916065"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#aaf23d79b0bdc047bdab776370c916065">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::operator++</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE InterleavedPredicatedTileIterator &amp; operator++()</div><div class="ttdoc">Advances to the next position to load or store. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:679</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_a17a6ccbe829782c27e49f47922fce84a"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a17a6ccbe829782c27e49f47922fce84a">cutlass::epilogue::threadblock::PredicatedTileIterator::clear_mask</a></div><div class="ttdeci">CUTLASS_DEVICE void clear_mask()</div><div class="ttdoc">Efficiently enables all accesses guarded by mask. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:422</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params_html_a5234c0bbe10bc907f90776dbce50d504"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a5234c0bbe10bc907f90776dbce50d504">cutlass::epilogue::threadblock::PredicatedTileIterator::Params::increment_group</a></div><div class="ttdeci">Index increment_group</div><div class="ttdoc">increment quantity (in bytes) to advance when moving to the next group </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:113</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_ae3464ad454dbb625b0013980998de9ad"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ae3464ad454dbb625b0013980998de9ad">cutlass::epilogue::threadblock::PredicatedTileIterator::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:76</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html">cutlass::epilogue::threadblock::PredicatedTileIterator</a></div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:65</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_ab340cbc86fcd16ac7c380dac35f5c699"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#ab340cbc86fcd16ac7c380dac35f5c699">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:463</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_aba9b6f085423136bf3cdd292ded36727"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#aba9b6f085423136bf3cdd292ded36727">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::set_mask</a></div><div class="ttdeci">CUTLASS_DEVICE void set_mask(Mask const &amp;mask)</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:714</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params_html_acfdbad18358373f86ef8f2f3eae62a1f"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Params.html#acfdbad18358373f86ef8f2f3eae62a1f">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params(Layout const &amp;layout)</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:515</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_a338508178242ceae7a137e343c7e7630"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a338508178242ceae7a137e343c7e7630">cutlass::epilogue::threadblock::PredicatedTileIterator::set_mask</a></div><div class="ttdeci">CUTLASS_DEVICE void set_mask(Mask const &amp;mask)</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:437</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask_html_acbd971f79b973257f09bacaa265f0520"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_1_1Mask.html#acbd971f79b973257f09bacaa265f0520">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::Mask::enable</a></div><div class="ttdeci">CUTLASS_DEVICE void enable()</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:547</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params_html_a010385e2b1e39fb0a42ce65c68e07e8e"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#a010385e2b1e39fb0a42ce65c68e07e8e">cutlass::epilogue::threadblock::PredicatedTileIterator::Params::advance_group</a></div><div class="ttdeci">Index advance_group</div><div class="ttdoc">amount to add to move to the next &amp;#39;group&amp;#39; position </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:117</div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_a12109679ba6ff108d8b4f633e60b9f5b"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a12109679ba6ff108d8b4f633e60b9f5b">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void add_pointer_offset(LongIndex pointer_offset)</div><div class="ttdoc">Adds a pointer offset in units of Element. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:630</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params_html_ad5da25e1dd34da92acbb00b25f4be7f5"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#ad5da25e1dd34da92acbb00b25f4be7f5">cutlass::epilogue::threadblock::PredicatedTileIterator::Params::initialize</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Status initialize(Index stride_)</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:126</div></div>
<div class="ttc" id="namespacecutlass_html_ac5a88c5840a28a9e0206b9cc7812a18da8c632159fa131f09d04f94e3cbcd8782"><div class="ttname"><a href="namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18da8c632159fa131f09d04f94e3cbcd8782">cutlass::Status::kSuccess</a></div><div class="ttdoc">Operation was successful. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_a879f53af759d71280cca8a8932002244"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a879f53af759d71280cca8a8932002244">cutlass::epilogue::threadblock::PredicatedTileIterator::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void add_pointer_offset(LongIndex pointer_offset)</div><div class="ttdoc">Adds a pointer offset in units of Element. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:273</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_ab63a8a4b7eef05d60729c45f43928b34"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ab63a8a4b7eef05d60729c45f43928b34">cutlass::epilogue::threadblock::PredicatedTileIterator::Shape</a></div><div class="ttdeci">typename ThreadMap::Shape Shape</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:68</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1ColumnMajorInterleaved_html"><div class="ttname"><a href="structcutlass_1_1layout_1_1ColumnMajorInterleaved.html">cutlass::layout::ColumnMajorInterleaved</a></div><div class="ttdef"><b>Definition:</b> layout/matrix.h:343</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_a487475c5bcda1d38d0d752f4b8c53d68"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a487475c5bcda1d38d0d752f4b8c53d68">cutlass::epilogue::threadblock::PredicatedTileIterator::TensorCoord</a></div><div class="ttdeci">MatrixCoord TensorCoord</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:78</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params_html_af884dcf4ef98ad19a5e9e5af9dfa3e40"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#af884dcf4ef98ad19a5e9e5af9dfa3e40">cutlass::epilogue::threadblock::PredicatedTileIterator::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params()</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:158</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_a6a5a5722a000c06b769e58de1e1c3903"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#a6a5a5722a000c06b769e58de1e1c3903">cutlass::epilogue::threadblock::PredicatedTileIterator::enable_mask</a></div><div class="ttdeci">CUTLASS_DEVICE void enable_mask()</div><div class="ttdoc">Sets the mask. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:427</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params_html_aca9106ffd4fe4e3d139cf01f3916bcba"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#aca9106ffd4fe4e3d139cf01f3916bcba">cutlass::epilogue::threadblock::PredicatedTileIterator::Params::increment_row</a></div><div class="ttdeci">Index increment_row</div><div class="ttdoc">increment quantity (in bytes) to advance when moving between rows </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:112</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params_html_af3e71c49f5c4830451ba7a70b960b772"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Params.html#af3e71c49f5c4830451ba7a70b960b772">cutlass::epilogue::threadblock::PredicatedTileIterator::Params::increment_cluster</a></div><div class="ttdeci">Index increment_cluster</div><div class="ttdoc">increment quantity (in bytes) to advance when moving to the next cluster </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:114</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_a8a7ceae2b239a602be46efb20cf34d04"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a8a7ceae2b239a602be46efb20cf34d04">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::get_mask</a></div><div class="ttdeci">CUTLASS_DEVICE void get_mask(Mask &amp;mask)</div><div class="ttdoc">Sets the mask. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:709</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html">cutlass::MatrixCoord</a></div><div class="ttdef"><b>Definition:</b> matrix_coord.h:39</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask_html_a0fcbcea35583d096e4154209237ba217"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#a0fcbcea35583d096e4154209237ba217">cutlass::epilogue::threadblock::PredicatedTileIterator::Mask::clear</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void clear()</div><div class="ttdoc">CUTLASS_HOST_DEVICE enables all accesses guarded by mask. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:186</div></div>
<div class="ttc" id="structcutlass_1_1layout_1_1PitchLinearCoord_html_aa828f8dbee3903754b56759c1e6a6043"><div class="ttname"><a href="structcutlass_1_1layout_1_1PitchLinearCoord.html#aa828f8dbee3903754b56759c1e6a6043">cutlass::layout::PitchLinearCoord::strided</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; strided() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> pitch_linear.h:97</div></div>
<div class="ttc" id="namespacecutlass_html_ac5a88c5840a28a9e0206b9cc7812a18d"><div class="ttname"><a href="namespacecutlass.html#ac5a88c5840a28a9e0206b9cc7812a18d">cutlass::Status</a></div><div class="ttdeci">Status</div><div class="ttdoc">Status code returned by CUTLASS operations. </div><div class="ttdef"><b>Definition:</b> cutlass.h:39</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator_html_a4d9f5f0439afd4f459ed22c2b0db9070"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1InterleavedPredicatedTileIterator.html#a4d9f5f0439afd4f459ed22c2b0db9070">cutlass::epilogue::threadblock::InterleavedPredicatedTileIterator::enable_mask</a></div><div class="ttdeci">CUTLASS_DEVICE void enable_mask()</div><div class="ttdoc">Sets the mask. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:704</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask_html_aefa401d42f20dd3740d90a410bcedc83"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_1_1Mask.html#aefa401d42f20dd3740d90a410bcedc83">cutlass::epilogue::threadblock::PredicatedTileIterator::Mask::Mask</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Mask()</div><div class="ttdoc">Efficiently disables all accesses guarded by mask. </div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:181</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator_html_ad6b1a44f14127ee55f70b8d2c043c67e"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1PredicatedTileIterator.html#ad6b1a44f14127ee55f70b8d2c043c67e">cutlass::epilogue::threadblock::PredicatedTileIterator::kThreads</a></div><div class="ttdeci">static int const kThreads</div><div class="ttdef"><b>Definition:</b> epilogue/threadblock/predicated_tile_iterator.h:81</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
