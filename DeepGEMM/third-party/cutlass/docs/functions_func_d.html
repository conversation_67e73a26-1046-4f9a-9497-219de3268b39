<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li class="current"><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_func.html"><span>Functions</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
      <li><a href="functions_type.html"><span>Typedefs</span></a></li>
      <li><a href="functions_enum.html"><span>Enumerations</span></a></li>
      <li><a href="functions_eval.html"><span>Enumerator</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="functions_func.html#index_a"><span>a</span></a></li>
      <li><a href="functions_func_b.html#index_b"><span>b</span></a></li>
      <li><a href="functions_func_c.html#index_c"><span>c</span></a></li>
      <li class="current"><a href="functions_func_d.html#index_d"><span>d</span></a></li>
      <li><a href="functions_func_e.html#index_e"><span>e</span></a></li>
      <li><a href="functions_func_f.html#index_f"><span>f</span></a></li>
      <li><a href="functions_func_g.html#index_g"><span>g</span></a></li>
      <li><a href="functions_func_h.html#index_h"><span>h</span></a></li>
      <li><a href="functions_func_i.html#index_i"><span>i</span></a></li>
      <li><a href="functions_func_k.html#index_k"><span>k</span></a></li>
      <li><a href="functions_func_l.html#index_l"><span>l</span></a></li>
      <li><a href="functions_func_m.html#index_m"><span>m</span></a></li>
      <li><a href="functions_func_n.html#index_n"><span>n</span></a></li>
      <li><a href="functions_func_o.html#index_o"><span>o</span></a></li>
      <li><a href="functions_func_p.html#index_p"><span>p</span></a></li>
      <li><a href="functions_func_q.html#index_q"><span>q</span></a></li>
      <li><a href="functions_func_r.html#index_r"><span>r</span></a></li>
      <li><a href="functions_func_s.html#index_s"><span>s</span></a></li>
      <li><a href="functions_func_t.html#index_t"><span>t</span></a></li>
      <li><a href="functions_func_u.html#index_u"><span>u</span></a></li>
      <li><a href="functions_func_v.html#index_v"><span>v</span></a></li>
      <li><a href="functions_func_w.html#index_w"><span>w</span></a></li>
      <li><a href="functions_func_0x7e.html#index_0x7e"><span>~</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_d"></a>- d -</h3><ul>
<li>data()
: <a class="el" href="structcutlass_1_1AlignedBuffer.html#a8ed8b9d3469621fc82d0041846c59da2">cutlass::AlignedBuffer&lt; T, N, Align &gt;</a>
, <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01false_01_4.html#a1949c8a8c81dc2743328a56ff19fc933">cutlass::Array&lt; T, N, false &gt;</a>
, <a class="el" href="classcutlass_1_1Array_3_01T_00_01N_00_01true_01_4.html#a3d3d2637b7051145a2048cff1b55c0bf">cutlass::Array&lt; T, N, true &gt;</a>
, <a class="el" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a2d57be4f0bdad670c7eb67e64dd1a9f5">cutlass::epilogue::threadblock::EpilogueBase&lt; Shape_, WarpMmaOperator_, PartitionsK, AccumulatorFragmentIterator_, WarpTileIterator_, Padding_ &gt;::SharedStorage</a>
, <a class="el" href="classcutlass_1_1TensorRef.html#ac7db3ca62ab1dfe0d3ea08bcadbc9352">cutlass::TensorRef&lt; Element_, Layout_ &gt;</a>
</li>
<li>debug_print()
: <a class="el" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#afd521c2dc754bb30024e8767bfc51e49">cutlass::epilogue::threadblock::EpilogueBase&lt; Shape_, WarpMmaOperator_, PartitionsK, AccumulatorFragmentIterator_, WarpTileIterator_, Padding_ &gt;::SharedStorage</a>
</li>
<li>DefaultBlockSwizzle()
: <a class="el" href="structcutlass_1_1reduction_1_1DefaultBlockSwizzle.html#a1ad8edda7b73d23fb5592a531f5736cc">cutlass::reduction::DefaultBlockSwizzle</a>
</li>
<li>denorm_min()
: <a class="el" href="structstd_1_1numeric__limits_3_01cutlass_1_1half__t_01_4.html#a2c05c19022c183e8734ada65c8970af5">std::numeric_limits&lt; cutlass::half_t &gt;</a>
</li>
<li>description()
: <a class="el" href="classcutlass_1_1library_1_1Operation.html#a62b9fbee4b72857214ca6c01874a27ce">cutlass::library::Operation</a>
</li>
<li>device_backed()
: <a class="el" href="classcutlass_1_1HostTensor.html#a73430856f79bedb64f9cf6b2044f38e3">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a>
</li>
<li>device_data()
: <a class="el" href="classcutlass_1_1HostTensor.html#aca2b28a16fc92d29102d00f154a1dfd1">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a>
</li>
<li>device_data_ptr_offset()
: <a class="el" href="classcutlass_1_1HostTensor.html#a81043b0539c8d18c40957411dd149e28">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a>
</li>
<li>device_ref()
: <a class="el" href="classcutlass_1_1HostTensor.html#a55a73e5ff7c7404c0bdee5f2b578b876">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a>
</li>
<li>device_view()
: <a class="el" href="classcutlass_1_1HostTensor.html#a6d1c49888cf678d3d5469eba4e911337">cutlass::HostTensor&lt; Element_, Layout_ &gt;</a>
</li>
<li>DirectEpilogueTensorOp()
: <a class="el" href="classcutlass_1_1epilogue_1_1threadblock_1_1DirectEpilogueTensorOp.html#a7a64b4523780869f4b7dde2225572b2f">cutlass::epilogue::threadblock::DirectEpilogueTensorOp&lt; Shape_, Operator_, PartitionsK, Element_, OutputOp_, ConvertOp_ &gt;</a>
</li>
<li>Distribution()
: <a class="el" href="structcutlass_1_1Distribution.html#a40f0b9d0f92199f8a49c931d34dd8c8a">cutlass::Distribution</a>
</li>
<li>dot()
: <a class="el" href="structcutlass_1_1Coord.html#a057a417a4d4a6e2f69e0b55a6f7ee902">cutlass::Coord&lt; Rank_, Index_, LongIndex_ &gt;</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
