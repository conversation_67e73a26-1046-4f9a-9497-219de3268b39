<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: epilogue_workspace.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_05a6795d99d74f63b7300fc6eb9e55c2.html">threadblock</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">epilogue_workspace.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="epilogue__workspace_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;  <span class="keyword">typename</span> Shape_,      </div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  <span class="keywordtype">int</span> WarpCount,        </div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keyword">typename</span> FragmentC_   </div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;&gt;</div><div class="line"><a name="l00064"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html">   64</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html">EpilogueWorkspace</a> {</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a3186c4180ee1a411dd967a14670c56b0">   67</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a3186c4180ee1a411dd967a14670c56b0">Shape</a> = Shape_;</div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a2a9e0715486050e4c52985f72392ba97">   68</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a2a9e0715486050e4c52985f72392ba97">FragmentC</a> = FragmentC_;</div><div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a5d61926055b8615580c11c3e8be2841e">   69</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a5d61926055b8615580c11c3e8be2841e">ElementC</a> = <span class="keyword">typename</span> FragmentC::value_type;</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a0bdff651696fabadbb71e6471eb3711b">   71</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a0bdff651696fabadbb71e6471eb3711b">kWarpCount</a> = WarpCount;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aad7937d2ef96522d0afa41a587c8f790">   74</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aad7937d2ef96522d0afa41a587c8f790">kAccessSizeInBits</a> = 128;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aa4f276efe10e1cbba8c994ef06313114">   77</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aa4f276efe10e1cbba8c994ef06313114">kWarpSize</a> = 32;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#ab5196507dba2b3252b53314596a0a770">   80</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#ab5196507dba2b3252b53314596a0a770">kElementsPerAccess</a> = </div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;    kAccessSizeInBits / <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;ElementC&gt;::value</a>;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a230e054d544fb2499fe0062a6c87eaae">   84</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a230e054d544fb2499fe0062a6c87eaae">kIterations</a> = FragmentC::kElements / <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#ab5196507dba2b3252b53314596a0a770">kElementsPerAccess</a>;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    !(FragmentC::kElements % kElementsPerAccess), </div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    <span class="stringliteral">&quot;The number of accumulators must be divisible by the access size.&quot;</span>);</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a808f585be1d17e849faf0fc58ab8bd8c">   91</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a808f585be1d17e849faf0fc58ab8bd8c">kWarpAccesses</a> = kIterations * <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aa4f276efe10e1cbba8c994ef06313114">kWarpSize</a>;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#ac0bb3a94eeabdeffbb2a2ae50e44fc2c">   94</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#ac0bb3a94eeabdeffbb2a2ae50e44fc2c">kThreadblockAccesses</a> = kWarpAccesses * <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a0bdff651696fabadbb71e6471eb3711b">kWarpCount</a>;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html">   97</a></span>&#160;  <span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html">Params</a> {</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div><div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a11183b5f5352f7c38442be03152f0210">  100</a></span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a5d61926055b8615580c11c3e8be2841e">ElementC</a> *<a class="code" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a11183b5f5352f7c38442be03152f0210">ptr_C</a>;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div><div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a51477610ec7f44d1a14c78072365ba4f">  103</a></span>&#160;    <span class="keywordtype">int</span> <a class="code" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a51477610ec7f44d1a14c78072365ba4f">stride_n</a>;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div><div class="line"><a name="l00106"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a680b0fd30ae273841ad2cdd1e7050467">  106</a></span>&#160;    <span class="keywordtype">int</span> <a class="code" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a680b0fd30ae273841ad2cdd1e7050467">stride_k</a>;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;    <span class="comment">// Methods</span></div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;    <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00113"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a1d05c8ac7337fa0437a7870e024b58e3">  113</a></span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a1d05c8ac7337fa0437a7870e024b58e3">Params</a>(</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;      <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a5d61926055b8615580c11c3e8be2841e">ElementC</a> *ptr_C,   </div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;      <span class="keywordtype">int</span> stride_n_,      </div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;      <span class="keywordtype">int</span> stride_k_       </div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;    ):</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;      ptr_C(ptr_C), stride_n(stride_n_ / kElementsPerAccess), stride_k(stride_k_ / kElementsPerAccess) {</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;    }</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;  };</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div><div class="line"><a name="l00124"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1SharedStorage.html">  124</a></span>&#160;  <span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1SharedStorage.html">SharedStorage</a> {</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    <span class="comment">// Intentionally empty</span></div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  };</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  <span class="keyword">struct </span><span class="keyword">alignas</span>((kAccessSizeInBits / 8)) AccessType {</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    Array&lt;ElementC, kElementsPerAccess&gt; storage;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  };</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  AccessType *pointer_;</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  <span class="keywordtype">int</span> stride_n_;</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  <span class="keywordtype">int</span> stride_k_;</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00147"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aeddb6d40413f06aea489f5d626717298">  147</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aeddb6d40413f06aea489f5d626717298">EpilogueWorkspace</a>(</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html">Params</a> <span class="keyword">const</span> &amp;params,     </div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1SharedStorage.html">SharedStorage</a> &amp;,          </div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;    <span class="keywordtype">int</span> warp_idx,             </div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    <span class="keywordtype">int</span> lane_idx              </div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;  ):</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    pointer_(reinterpret_cast&lt;AccessType *&gt;(params.<a class="code" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a11183b5f5352f7c38442be03152f0210">ptr_C</a>)),</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    stride_n_(params.<a class="code" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a51477610ec7f44d1a14c78072365ba4f">stride_n</a>), </div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    stride_k_(params.<a class="code" href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a680b0fd30ae273841ad2cdd1e7050467">stride_k</a>) {</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;    <span class="comment">// Add per-thread offset</span></div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;    pointer_ += lane_idx + warp_idx * <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a808f585be1d17e849faf0fc58ab8bd8c">kWarpAccesses</a>;</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;  }</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00164"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a40eda72233c29dd344513b21ae94db0e">  164</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a40eda72233c29dd344513b21ae94db0e">operator()</a>(</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;    <a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">cutlass::gemm::GemmCoord</a> problem_size,       </div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;    <a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">cutlass::gemm::GemmCoord</a> tb_tile_coord,      </div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a2a9e0715486050e4c52985f72392ba97">FragmentC</a> <span class="keyword">const</span> &amp;accum) {     </div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    </div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;    <span class="comment">// Compute offset for entire threadblock (note, per-thread offset has been folded in already)</span></div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    AccessType *pointer = pointer_ + </div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;      tb_tile_coord.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a93515a41db6c4b7e9101067f60d41b8c">m</a>() * kThreadblockAccesses + </div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;      tb_tile_coord.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a1b29d2cb15360ad5499216859ad5436a">n</a>() * stride_n_ +</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;      tb_tile_coord.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a18835ec84cbb6250143327e93697c7e9">k</a>() * stride_k_;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;    <span class="comment">// Cast to vectorized view of accumulator fragments</span></div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;    AccessType <span class="keyword">const</span> * src_pointer = <span class="keyword">reinterpret_cast&lt;</span>AccessType <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;accum);</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    <span class="comment">// Write out accumulators at full speed</span></div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a230e054d544fb2499fe0062a6c87eaae">kIterations</a>; ++i) {</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;      pointer[i * <a class="code" href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aa4f276efe10e1cbba8c994ef06313114">kWarpSize</a>] = src_pointer[i];</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;    }</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;  }</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;};</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;</div><div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1SharedStorage_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1SharedStorage.html">cutlass::epilogue::EpilogueWorkspace::SharedStorage</a></div><div class="ttdoc">Shared storage allocation needed by the epilogue. </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:124</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html_aad7937d2ef96522d0afa41a587c8f790"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aad7937d2ef96522d0afa41a587c8f790">cutlass::epilogue::EpilogueWorkspace::kAccessSizeInBits</a></div><div class="ttdeci">static int const kAccessSizeInBits</div><div class="ttdoc">Optimize for 128b accesses. </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:74</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html">cutlass::gemm::GemmCoord</a></div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:94</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html_a808f585be1d17e849faf0fc58ab8bd8c"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a808f585be1d17e849faf0fc58ab8bd8c">cutlass::epilogue::EpilogueWorkspace::kWarpAccesses</a></div><div class="ttdeci">static int const kWarpAccesses</div><div class="ttdoc">Total number of vectorized accesses in warp (in units of vector) </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:91</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params_html_a1d05c8ac7337fa0437a7870e024b58e3"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a1d05c8ac7337fa0437a7870e024b58e3">cutlass::epilogue::EpilogueWorkspace::Params::Params</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Params(ElementC *ptr_C, int stride_n_, int stride_k_)</div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:113</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html_a1b29d2cb15360ad5499216859ad5436a"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html#a1b29d2cb15360ad5499216859ad5436a">cutlass::gemm::GemmCoord::n</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; n() const </div><div class="ttdoc">Returns the GEMM N coordinate. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:137</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html_a230e054d544fb2499fe0062a6c87eaae"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a230e054d544fb2499fe0062a6c87eaae">cutlass::epilogue::EpilogueWorkspace::kIterations</a></div><div class="ttdeci">static int const kIterations</div><div class="ttdoc">Number of stores per thread. </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:84</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html_a40eda72233c29dd344513b21ae94db0e"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a40eda72233c29dd344513b21ae94db0e">cutlass::epilogue::EpilogueWorkspace::operator()</a></div><div class="ttdeci">CUTLASS_DEVICE void operator()(cutlass::gemm::GemmCoord problem_size, cutlass::gemm::GemmCoord tb_tile_coord, FragmentC const &amp;accum)</div><div class="ttdoc">Streams the result to global memory. </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:164</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html_a3186c4180ee1a411dd967a14670c56b0"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a3186c4180ee1a411dd967a14670c56b0">cutlass::epilogue::EpilogueWorkspace::Shape</a></div><div class="ttdeci">Shape_ Shape</div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:67</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html_a18835ec84cbb6250143327e93697c7e9"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html#a18835ec84cbb6250143327e93697c7e9">cutlass::gemm::GemmCoord::k</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; k() const </div><div class="ttdoc">Returns the GEMM K coordinate. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:145</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html_a5d61926055b8615580c11c3e8be2841e"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a5d61926055b8615580c11c3e8be2841e">cutlass::epilogue::EpilogueWorkspace::ElementC</a></div><div class="ttdeci">typename FragmentC::value_type ElementC</div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:69</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="structcutlass_1_1sizeof__bits_html"><div class="ttname"><a href="structcutlass_1_1sizeof__bits.html">cutlass::sizeof_bits</a></div><div class="ttdoc">Defines the size of an element in bits. </div><div class="ttdef"><b>Definition:</b> numeric_types.h:42</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params_html_a11183b5f5352f7c38442be03152f0210"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a11183b5f5352f7c38442be03152f0210">cutlass::epilogue::EpilogueWorkspace::Params::ptr_C</a></div><div class="ttdeci">ElementC * ptr_C</div><div class="ttdoc">Pointer to C matrix. </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:100</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html_a2a9e0715486050e4c52985f72392ba97"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a2a9e0715486050e4c52985f72392ba97">cutlass::epilogue::EpilogueWorkspace::FragmentC</a></div><div class="ttdeci">FragmentC_ FragmentC</div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:68</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html_aeddb6d40413f06aea489f5d626717298"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aeddb6d40413f06aea489f5d626717298">cutlass::epilogue::EpilogueWorkspace::EpilogueWorkspace</a></div><div class="ttdeci">CUTLASS_DEVICE EpilogueWorkspace(Params const &amp;params, SharedStorage &amp;, int warp_idx, int lane_idx)</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:147</div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html_a0bdff651696fabadbb71e6471eb3711b"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#a0bdff651696fabadbb71e6471eb3711b">cutlass::epilogue::EpilogueWorkspace::kWarpCount</a></div><div class="ttdeci">static int const kWarpCount</div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:71</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params_html_a51477610ec7f44d1a14c78072365ba4f"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a51477610ec7f44d1a14c78072365ba4f">cutlass::epilogue::EpilogueWorkspace::Params::stride_n</a></div><div class="ttdeci">int stride_n</div><div class="ttdoc">Stride between tiles along the GEMM N dimension (in units of vectors) </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:103</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html_ab5196507dba2b3252b53314596a0a770"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#ab5196507dba2b3252b53314596a0a770">cutlass::epilogue::EpilogueWorkspace::kElementsPerAccess</a></div><div class="ttdeci">static int const kElementsPerAccess</div><div class="ttdoc">Vector length of accesses. </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:80</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html_a93515a41db6c4b7e9101067f60d41b8c"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html#a93515a41db6c4b7e9101067f60d41b8c">cutlass::gemm::GemmCoord::m</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; m() const </div><div class="ttdoc">Returns the GEMM M coordinate. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:129</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params_html_a680b0fd30ae273841ad2cdd1e7050467"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html#a680b0fd30ae273841ad2cdd1e7050467">cutlass::epilogue::EpilogueWorkspace::Params::stride_k</a></div><div class="ttdeci">int stride_k</div><div class="ttdoc">Stride between tiles along the GEMM K dimension (in units of vectors) </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:106</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1EpilogueWorkspace_1_1Params.html">cutlass::epilogue::EpilogueWorkspace::Params</a></div><div class="ttdoc">Parameters structure. </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:97</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html">cutlass::epilogue::EpilogueWorkspace</a></div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:64</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html_aa4f276efe10e1cbba8c994ef06313114"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#aa4f276efe10e1cbba8c994ef06313114">cutlass::epilogue::EpilogueWorkspace::kWarpSize</a></div><div class="ttdeci">static int const kWarpSize</div><div class="ttdoc">Warp size from the perspective of memory operations. </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:77</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1EpilogueWorkspace_html_ac0bb3a94eeabdeffbb2a2ae50e44fc2c"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1EpilogueWorkspace.html#ac0bb3a94eeabdeffbb2a2ae50e44fc2c">cutlass::epilogue::EpilogueWorkspace::kThreadblockAccesses</a></div><div class="ttdeci">static int const kThreadblockAccesses</div><div class="ttdoc">Total number of vectorized accesses in threadblock tile (in units of vector) </div><div class="ttdef"><b>Definition:</b> epilogue_workspace.h:94</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
