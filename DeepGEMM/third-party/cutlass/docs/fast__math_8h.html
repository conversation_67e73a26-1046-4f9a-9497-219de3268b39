<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: fast_math.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#namespaces">Namespaces</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">fast_math.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Math utilities.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;cstdint&gt;</code><br />
<code>#include &quot;<a class="el" href="cutlass_8h_source.html">cutlass/cutlass.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for fast_math.h:</div>
<div class="dyncontent">
<div class="center"><img src="fast__math_8h__incl.png" border="0" usemap="#fast__math_8h" alt=""/></div>
<map name="fast__math_8h" id="fast__math_8h">
</map>
</div>
</div><div class="textblock"><div class="dynheader">
This graph shows which files directly or indirectly include this file:</div>
<div class="dyncontent">
<div class="center"><img src="fast__math_8h__dep__incl.png" border="0" usemap="#fast__math_8hdep" alt=""/></div>
<map name="fast__math_8hdep" id="fast__math_8hdep">
</map>
</div>
</div>
<p><a href="fast__math_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1is__pow2.html">cutlass::is_pow2&lt; N &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1log2__down.html">cutlass::log2_down&lt; N, CurrentVal, Count &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1log2__down_3_01N_00_011_00_01Count_01_4.html">cutlass::log2_down&lt; N, 1, Count &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1log2__up.html">cutlass::log2_up&lt; N, CurrentVal, Count &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1log2__up_3_01N_00_011_00_01Count_01_4.html">cutlass::log2_up&lt; N, 1, Count &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1sqrt__est.html">cutlass::sqrt_est&lt; N &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1divide__assert.html">cutlass::divide_assert&lt; Dividend, Divisor &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Min.html">cutlass::Min&lt; A, B &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1Max.html">cutlass::Max&lt; A, B &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespacecutlass"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html">cutlass</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a17c8c408d672d26f1c70d2435f6ac83e"><td class="memTemplParams" colspan="2">template&lt;typename dividend_t , typename divisor_t &gt; </td></tr>
<tr class="memitem:a17c8c408d672d26f1c70d2435f6ac83e"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> dividend_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#a17c8c408d672d26f1c70d2435f6ac83e">cutlass::round_nearest</a> (dividend_t dividend, divisor_t divisor)</td></tr>
<tr class="separator:a17c8c408d672d26f1c70d2435f6ac83e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38481ebfe13bc199aa621ceecfa016b8"><td class="memTemplParams" colspan="2">template&lt;typename value_t &gt; </td></tr>
<tr class="memitem:a38481ebfe13bc199aa621ceecfa016b8"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> value_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#a38481ebfe13bc199aa621ceecfa016b8">cutlass::gcd</a> (value_t a, value_t b)</td></tr>
<tr class="separator:a38481ebfe13bc199aa621ceecfa016b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af07506fee11de882d926f4e8237eef09"><td class="memTemplParams" colspan="2">template&lt;typename value_t &gt; </td></tr>
<tr class="memitem:af07506fee11de882d926f4e8237eef09"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> value_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#af07506fee11de882d926f4e8237eef09">cutlass::lcm</a> (value_t a, value_t b)</td></tr>
<tr class="separator:af07506fee11de882d926f4e8237eef09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6bc666acc9f0d7278a788975e226e005"><td class="memTemplParams" colspan="2">template&lt;typename value_t &gt; </td></tr>
<tr class="memitem:a6bc666acc9f0d7278a788975e226e005"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> value_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#a6bc666acc9f0d7278a788975e226e005">cutlass::clz</a> (value_t x)</td></tr>
<tr class="separator:a6bc666acc9f0d7278a788975e226e005"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58a119c3f7b33d97c43ae8c114004d9e"><td class="memTemplParams" colspan="2">template&lt;typename value_t &gt; </td></tr>
<tr class="memitem:a58a119c3f7b33d97c43ae8c114004d9e"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> value_t&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#a58a119c3f7b33d97c43ae8c114004d9e">cutlass::find_log2</a> (value_t x)</td></tr>
<tr class="separator:a58a119c3f7b33d97c43ae8c114004d9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac63a770acddafd828619834cf2c99d3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#aac63a770acddafd828619834cf2c99d3">cutlass::find_divisor</a> (unsigned int &amp;mul, unsigned int &amp;shr, unsigned int denom)</td></tr>
<tr class="separator:aac63a770acddafd828619834cf2c99d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab9726f5a6b39322cf13cd916257fd9a7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#ab9726f5a6b39322cf13cd916257fd9a7">cutlass::fast_divmod</a> (int &amp;quo, int &amp;rem, int src, int div, unsigned int mul, unsigned int shr)</td></tr>
<tr class="separator:ab9726f5a6b39322cf13cd916257fd9a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae051c9a8142f8ccea23a3998a7c4a8dc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#ae051c9a8142f8ccea23a3998a7c4a8dc">cutlass::fast_divmod</a> (int &amp;quo, int64_t &amp;rem, int64_t src, int div, unsigned int mul, unsigned int shr)</td></tr>
<tr class="separator:ae051c9a8142f8ccea23a3998a7c4a8dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1676e17a7fea0ac40d9d239cbd3ce872"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#a1676e17a7fea0ac40d9d239cbd3ce872">cutlass::const_min</a> (int a, int b)</td></tr>
<tr class="separator:a1676e17a7fea0ac40d9d239cbd3ce872"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a072919006084ca52479a69cd10694448"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html#a072919006084ca52479a69cd10694448">cutlass::const_max</a> (int a, int b)</td></tr>
<tr class="separator:a072919006084ca52479a69cd10694448"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
