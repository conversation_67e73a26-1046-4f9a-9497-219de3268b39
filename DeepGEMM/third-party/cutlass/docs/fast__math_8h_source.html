<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: fast_math.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">fast_math.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="fast__math_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#include &lt;cstdint&gt;</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="comment">/******************************************************************************</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="comment"> * Static math utilities</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="comment"> ******************************************************************************/</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="structcutlass_1_1is__pow2.html">   46</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1is__pow2.html">is_pow2</a> {</div><div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="structcutlass_1_1is__pow2.html#acdfaa8d35a35ed129d4cdb969e813252">   47</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">bool</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1is__pow2.html#acdfaa8d35a35ed129d4cdb969e813252">value</a> = ((N &amp; (N - 1)) == 0);</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;};</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> N, <span class="keywordtype">int</span> CurrentVal = N, <span class="keywordtype">int</span> Count = 0&gt;</div><div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="structcutlass_1_1log2__down.html">   54</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1log2__down.html">log2_down</a> {</div><div class="line"><a name="l00056"></a><span class="lineno"><a class="line" href="structcutlass_1_1log2__down.html#a4152ab42ed34b9011b50bcadce09a9d5a23d1b50f2f02e1026d4b5dc7ebd6880d">   56</a></span>&#160;  <span class="keyword">enum</span> { <a class="code" href="structcutlass_1_1is__pow2.html#acdfaa8d35a35ed129d4cdb969e813252">value</a> = <a class="code" href="structcutlass_1_1log2__down.html">log2_down&lt;N, (CurrentVal &gt;</a>&gt; 1), Count + 1&gt;::<a class="code" href="structcutlass_1_1is__pow2.html#acdfaa8d35a35ed129d4cdb969e813252">value</a> };</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;};</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="comment">// Base case</span></div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> N, <span class="keywordtype">int</span> Count&gt;</div><div class="line"><a name="l00061"></a><span class="lineno"><a class="line" href="structcutlass_1_1log2__down_3_01N_00_011_00_01Count_01_4.html">   61</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1log2__down.html">log2_down</a>&lt;N, 1, Count&gt; {</div><div class="line"><a name="l00062"></a><span class="lineno"><a class="line" href="structcutlass_1_1log2__down_3_01N_00_011_00_01Count_01_4.html#a5557a2049ecabc297e8a1d97fde4d4d7a282c4c5d8f66dc49544f34071f148b1f">   62</a></span>&#160;  <span class="keyword">enum</span> { <a class="code" href="structcutlass_1_1is__pow2.html#acdfaa8d35a35ed129d4cdb969e813252">value</a> = Count };</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;};</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> N, <span class="keywordtype">int</span> CurrentVal = N, <span class="keywordtype">int</span> Count = 0&gt;</div><div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="structcutlass_1_1log2__up.html">   69</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1log2__up.html">log2_up</a> {</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="structcutlass_1_1log2__up.html#ad0bb5e85c86376923a4f6008e0ee3d50a09591054a7c9b184769d579c56dd09d6">   71</a></span>&#160;  <span class="keyword">enum</span> { <a class="code" href="structcutlass_1_1is__pow2.html#acdfaa8d35a35ed129d4cdb969e813252">value</a> = <a class="code" href="structcutlass_1_1log2__up.html">log2_up&lt;N, (CurrentVal &gt;</a>&gt; 1), Count + 1&gt;::<a class="code" href="structcutlass_1_1is__pow2.html#acdfaa8d35a35ed129d4cdb969e813252">value</a> };</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;};</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;<span class="comment">// Base case</span></div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> N, <span class="keywordtype">int</span> Count&gt;</div><div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="structcutlass_1_1log2__up_3_01N_00_011_00_01Count_01_4.html">   76</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1log2__up.html">log2_up</a>&lt;N, 1, Count&gt; {</div><div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="structcutlass_1_1log2__up_3_01N_00_011_00_01Count_01_4.html#a29b8082ce3fef34fe055a978973fee6ca6b6af5b6bf14ee5d3e3f1442e7f75117">   77</a></span>&#160;  <span class="keyword">enum</span> { <a class="code" href="structcutlass_1_1is__pow2.html#acdfaa8d35a35ed129d4cdb969e813252">value</a> = ((1 &lt;&lt; Count) &lt; N) ? Count + 1 : Count };</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;};</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> N&gt;</div><div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="structcutlass_1_1sqrt__est.html">   84</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1sqrt__est.html">sqrt_est</a> {</div><div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="structcutlass_1_1sqrt__est.html#ab199edc94a4c60069536cb3d936f50f5a2e73d046302be2504f50c08d788e9964">   85</a></span>&#160;  <span class="keyword">enum</span> { <a class="code" href="structcutlass_1_1is__pow2.html#acdfaa8d35a35ed129d4cdb969e813252">value</a> = 1 &lt;&lt; (<a class="code" href="structcutlass_1_1log2__up.html">log2_up&lt;N&gt;::value</a> / 2) };</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;};</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Div<span class="keywordtype">id</span>end, <span class="keywordtype">int</span> Divisor&gt;</div><div class="line"><a name="l00093"></a><span class="lineno"><a class="line" href="structcutlass_1_1divide__assert.html">   93</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1divide__assert.html">divide_assert</a> {</div><div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="structcutlass_1_1divide__assert.html#a86eb818da255fb6ad4640991b3706c9fab924a64662c2eb917b1dd4ca31fdd2dc">   94</a></span>&#160;  <span class="keyword">enum</span> { <a class="code" href="structcutlass_1_1is__pow2.html#acdfaa8d35a35ed129d4cdb969e813252">value</a> = Dividend / Divisor };</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>((Dividend % Divisor == 0), <span class="stringliteral">&quot;Not an even multiple&quot;</span>);</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;};</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="comment">/******************************************************************************</span></div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;<span class="comment"> * Rounding</span></div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;<span class="comment"> ******************************************************************************/</span></div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> div<span class="keywordtype">id</span>end_t, <span class="keyword">typename</span> divisor_t&gt;</div><div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="namespacecutlass.html#a17c8c408d672d26f1c70d2435f6ac83e">  107</a></span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> dividend_t <a class="code" href="namespacecutlass.html#a17c8c408d672d26f1c70d2435f6ac83e">round_nearest</a>(dividend_t dividend, divisor_t divisor) {</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;  <span class="keywordflow">return</span> ((dividend + divisor - 1) / divisor) * divisor;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;}</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> value_t&gt;</div><div class="line"><a name="l00115"></a><span class="lineno"><a class="line" href="namespacecutlass.html#a38481ebfe13bc199aa621ceecfa016b8">  115</a></span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> value_t <a class="code" href="namespacecutlass.html#a38481ebfe13bc199aa621ceecfa016b8">gcd</a>(value_t a, value_t b) {</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;  <span class="keywordflow">for</span> (;;) {</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;    <span class="keywordflow">if</span> (a == 0) <span class="keywordflow">return</span> b;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;    b %= a;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;    <span class="keywordflow">if</span> (b == 0) <span class="keywordflow">return</span> a;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;    a %= b;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;  }</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;}</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> value_t&gt;</div><div class="line"><a name="l00128"></a><span class="lineno"><a class="line" href="namespacecutlass.html#af07506fee11de882d926f4e8237eef09">  128</a></span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> value_t <a class="code" href="namespacecutlass.html#af07506fee11de882d926f4e8237eef09">lcm</a>(value_t a, value_t b) {</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  value_t temp = <a class="code" href="namespacecutlass.html#a38481ebfe13bc199aa621ceecfa016b8">gcd</a>(a, b);</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  <span class="keywordflow">return</span> temp ? (a / temp * b) : 0;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;}</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> value_t&gt;</div><div class="line"><a name="l00140"></a><span class="lineno"><a class="line" href="namespacecutlass.html#a6bc666acc9f0d7278a788975e226e005">  140</a></span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> value_t <a class="code" href="namespacecutlass.html#a6bc666acc9f0d7278a788975e226e005">clz</a>(value_t x) {</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 31; i &gt;= 0; --i) {</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;    <span class="keywordflow">if</span> ((1 &lt;&lt; i) &amp; x) <span class="keywordflow">return</span> 31 - i;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;  }</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  <span class="keywordflow">return</span> 32;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;}</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> value_t&gt;</div><div class="line"><a name="l00148"></a><span class="lineno"><a class="line" href="namespacecutlass.html#a58a119c3f7b33d97c43ae8c114004d9e">  148</a></span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> value_t <a class="code" href="namespacecutlass.html#a58a119c3f7b33d97c43ae8c114004d9e">find_log2</a>(value_t x) {</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;  <span class="keywordtype">int</span> a = int(31 - <a class="code" href="namespacecutlass.html#a6bc666acc9f0d7278a788975e226e005">clz</a>(x));</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;  a += (x &amp; (x - 1)) != 0;  <span class="comment">// Round up, add 1 if not a power of 2.</span></div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;  <span class="keywordflow">return</span> a;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;}</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> </div><div class="line"><a name="l00159"></a><span class="lineno"><a class="line" href="namespacecutlass.html#aac63a770acddafd828619834cf2c99d3">  159</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass.html#aac63a770acddafd828619834cf2c99d3">find_divisor</a>(<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span>&amp; mul, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span>&amp; shr, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> denom) {</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;  <span class="keywordflow">if</span> (denom == 1) {</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;    mul = 0;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    shr = 0;</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;  } <span class="keywordflow">else</span> {</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> p = 31 + <a class="code" href="namespacecutlass.html#a58a119c3f7b33d97c43ae8c114004d9e">find_log2</a>(denom);</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;    <span class="keywordtype">unsigned</span> m = unsigned(((1ull &lt;&lt; p) + <span class="keywordtype">unsigned</span>(denom) - 1) / <span class="keywordtype">unsigned</span>(denom));</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;    mul = m;</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    shr = p - 32;</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;  }</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;}</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> </div><div class="line"><a name="l00176"></a><span class="lineno"><a class="line" href="namespacecutlass.html#ab9726f5a6b39322cf13cd916257fd9a7">  176</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass.html#ab9726f5a6b39322cf13cd916257fd9a7">fast_divmod</a>(<span class="keywordtype">int</span>&amp; quo, <span class="keywordtype">int</span>&amp; rem, <span class="keywordtype">int</span> src, <span class="keywordtype">int</span> div, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> mul, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> shr) {</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;<span class="preprocessor">  #if defined(__CUDA_ARCH__)</span></div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;  <span class="comment">// Use IMUL.HI if div != 1, else simply copy the source.</span></div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;  quo = (div != 1) ? __umulhi(src, mul) &gt;&gt; shr : src;</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;<span class="preprocessor">  #else</span></div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;  quo = int((div != 1) ? <span class="keywordtype">int</span>(src * mul) &gt;&gt; shr : src);</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;<span class="preprocessor">  #endif</span></div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;  <span class="comment">// The remainder.</span></div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;  rem = src - (quo * div);</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;}</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;<span class="comment">// For long int input</span></div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00192"></a><span class="lineno"><a class="line" href="namespacecutlass.html#ae051c9a8142f8ccea23a3998a7c4a8dc">  192</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass.html#ab9726f5a6b39322cf13cd916257fd9a7">fast_divmod</a>(<span class="keywordtype">int</span>&amp; quo, int64_t&amp; rem, int64_t src, <span class="keywordtype">int</span> div, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> mul, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> shr) {</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;<span class="preprocessor">  #if defined(__CUDA_ARCH__)</span></div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;  <span class="comment">// Use IMUL.HI if div != 1, else simply copy the source.</span></div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;  quo = (div != 1) ? __umulhi(src, mul) &gt;&gt; shr : src;</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;<span class="preprocessor">  #else</span></div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;  quo = int((div != 1) ? (src * mul) &gt;&gt; shr : src);</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;<span class="preprocessor">  #endif</span></div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;  <span class="comment">// The remainder.</span></div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;  rem = src - (quo * div);</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;}</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;<span class="comment">/******************************************************************************</span></div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;<span class="comment"> * Min/Max</span></div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;<span class="comment"> ******************************************************************************/</span></div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> A, <span class="keywordtype">int</span> B&gt;</div><div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="structcutlass_1_1Min.html">  209</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1Min.html">Min</a> {</div><div class="line"><a name="l00210"></a><span class="lineno"><a class="line" href="structcutlass_1_1Min.html#a97e6dd3ff6fb5404e8a6e6109f73f429">  210</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kValue = (A &lt; B) ? A : B;</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;};</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> A, <span class="keywordtype">int</span> B&gt;</div><div class="line"><a name="l00214"></a><span class="lineno"><a class="line" href="structcutlass_1_1Max.html">  214</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1Max.html">Max</a> {</div><div class="line"><a name="l00215"></a><span class="lineno"><a class="line" href="structcutlass_1_1Max.html#a6ed8be7ed855eea8f8d08921f7b5d763">  215</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kValue = (A &gt; B) ? A : B;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;};</div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00219"></a><span class="lineno"><a class="line" href="namespacecutlass.html#a1676e17a7fea0ac40d9d239cbd3ce872">  219</a></span>&#160;<a class="code" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <span class="keywordtype">int</span> <a class="code" href="namespacecutlass.html#a1676e17a7fea0ac40d9d239cbd3ce872">const_min</a>(<span class="keywordtype">int</span> a, <span class="keywordtype">int</span> b) {</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;    <span class="keywordflow">return</span> (b &lt; a ? b : a);</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;}</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00224"></a><span class="lineno"><a class="line" href="namespacecutlass.html#a072919006084ca52479a69cd10694448">  224</a></span>&#160;<a class="code" href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a> <span class="keywordtype">int</span> <a class="code" href="namespacecutlass.html#a072919006084ca52479a69cd10694448">const_max</a>(<span class="keywordtype">int</span> a, <span class="keywordtype">int</span> b) {</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;    <span class="keywordflow">return</span> (b &gt; a ? b : a);</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;}</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;}  <span class="comment">// namespace cutlass</span></div><div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="platform_8h_html_a72f0657181cca64b44eb186b707eb380"><div class="ttname"><a href="platform_8h.html#a72f0657181cca64b44eb186b707eb380">constexpr</a></div><div class="ttdeci">#define constexpr</div><div class="ttdef"><b>Definition:</b> platform.h:137</div></div>
<div class="ttc" id="namespacecutlass_html_ab9726f5a6b39322cf13cd916257fd9a7"><div class="ttname"><a href="namespacecutlass.html#ab9726f5a6b39322cf13cd916257fd9a7">cutlass::fast_divmod</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void fast_divmod(int &amp;quo, int &amp;rem, int src, int div, unsigned int mul, unsigned int shr)</div><div class="ttdef"><b>Definition:</b> fast_math.h:176</div></div>
<div class="ttc" id="namespacecutlass_html_a58a119c3f7b33d97c43ae8c114004d9e"><div class="ttname"><a href="namespacecutlass.html#a58a119c3f7b33d97c43ae8c114004d9e">cutlass::find_log2</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE value_t find_log2(value_t x)</div><div class="ttdef"><b>Definition:</b> fast_math.h:148</div></div>
<div class="ttc" id="structcutlass_1_1log2__down_html"><div class="ttname"><a href="structcutlass_1_1log2__down.html">cutlass::log2_down</a></div><div class="ttdef"><b>Definition:</b> fast_math.h:54</div></div>
<div class="ttc" id="structcutlass_1_1Min_html"><div class="ttname"><a href="structcutlass_1_1Min.html">cutlass::Min</a></div><div class="ttdef"><b>Definition:</b> fast_math.h:209</div></div>
<div class="ttc" id="namespacecutlass_html_a072919006084ca52479a69cd10694448"><div class="ttname"><a href="namespacecutlass.html#a072919006084ca52479a69cd10694448">cutlass::const_max</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE constexpr int const_max(int a, int b)</div><div class="ttdef"><b>Definition:</b> fast_math.h:224</div></div>
<div class="ttc" id="structcutlass_1_1is__pow2_html_acdfaa8d35a35ed129d4cdb969e813252"><div class="ttname"><a href="structcutlass_1_1is__pow2.html#acdfaa8d35a35ed129d4cdb969e813252">cutlass::is_pow2::value</a></div><div class="ttdeci">static bool const value</div><div class="ttdef"><b>Definition:</b> fast_math.h:47</div></div>
<div class="ttc" id="namespacecutlass_html_af07506fee11de882d926f4e8237eef09"><div class="ttname"><a href="namespacecutlass.html#af07506fee11de882d926f4e8237eef09">cutlass::lcm</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE value_t lcm(value_t a, value_t b)</div><div class="ttdef"><b>Definition:</b> fast_math.h:128</div></div>
<div class="ttc" id="namespacecutlass_html_a17c8c408d672d26f1c70d2435f6ac83e"><div class="ttname"><a href="namespacecutlass.html#a17c8c408d672d26f1c70d2435f6ac83e">cutlass::round_nearest</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE dividend_t round_nearest(dividend_t dividend, divisor_t divisor)</div><div class="ttdef"><b>Definition:</b> fast_math.h:107</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="structcutlass_1_1Max_html"><div class="ttname"><a href="structcutlass_1_1Max.html">cutlass::Max</a></div><div class="ttdef"><b>Definition:</b> fast_math.h:214</div></div>
<div class="ttc" id="namespacecutlass_html_aac63a770acddafd828619834cf2c99d3"><div class="ttname"><a href="namespacecutlass.html#aac63a770acddafd828619834cf2c99d3">cutlass::find_divisor</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void find_divisor(unsigned int &amp;mul, unsigned int &amp;shr, unsigned int denom)</div><div class="ttdef"><b>Definition:</b> fast_math.h:159</div></div>
<div class="ttc" id="namespacecutlass_html_a38481ebfe13bc199aa621ceecfa016b8"><div class="ttname"><a href="namespacecutlass.html#a38481ebfe13bc199aa621ceecfa016b8">cutlass::gcd</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE value_t gcd(value_t a, value_t b)</div><div class="ttdef"><b>Definition:</b> fast_math.h:115</div></div>
<div class="ttc" id="structcutlass_1_1divide__assert_html"><div class="ttname"><a href="structcutlass_1_1divide__assert.html">cutlass::divide_assert</a></div><div class="ttdef"><b>Definition:</b> fast_math.h:93</div></div>
<div class="ttc" id="structcutlass_1_1log2__up_html"><div class="ttname"><a href="structcutlass_1_1log2__up.html">cutlass::log2_up</a></div><div class="ttdef"><b>Definition:</b> fast_math.h:69</div></div>
<div class="ttc" id="namespacecutlass_html_a6bc666acc9f0d7278a788975e226e005"><div class="ttname"><a href="namespacecutlass.html#a6bc666acc9f0d7278a788975e226e005">cutlass::clz</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE value_t clz(value_t x)</div><div class="ttdef"><b>Definition:</b> fast_math.h:140</div></div>
<div class="ttc" id="structcutlass_1_1is__pow2_html"><div class="ttname"><a href="structcutlass_1_1is__pow2.html">cutlass::is_pow2</a></div><div class="ttdef"><b>Definition:</b> fast_math.h:46</div></div>
<div class="ttc" id="namespacecutlass_html_a1676e17a7fea0ac40d9d239cbd3ce872"><div class="ttname"><a href="namespacecutlass.html#a1676e17a7fea0ac40d9d239cbd3ce872">cutlass::const_min</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE constexpr int const_min(int a, int b)</div><div class="ttdef"><b>Definition:</b> fast_math.h:219</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1sqrt__est_html"><div class="ttname"><a href="structcutlass_1_1sqrt__est.html">cutlass::sqrt_est</a></div><div class="ttdef"><b>Definition:</b> fast_math.h:84</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
