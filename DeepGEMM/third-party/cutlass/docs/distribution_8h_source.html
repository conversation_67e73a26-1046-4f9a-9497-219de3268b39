<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: distribution.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">distribution.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="distribution_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &lt;fstream&gt;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html">   38</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1Distribution.html">Distribution</a> {</div><div class="line"><a name="l00040"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa0fad91cf4fcbc8ab015053bea77090a6">   40</a></span>&#160;  <span class="keyword">enum</span> <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92a">Kind</a> { <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa2ff14122c59a823654b84764f68e597b">Invalid</a>, <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa0fad91cf4fcbc8ab015053bea77090a6">Uniform</a>, <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa39890d8be86d514207259b1b5dca3ed5">Gaussian</a>, <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa08adbc111dc827d524a5509c948d8ba6">Identity</a>, <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa39d3cf55e90573c8d1dfb483cfb410dc">Sequential</a> };</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;  <span class="keyword">union </span>{</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;    <span class="keyword">struct </span>{</div><div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#a846430e3a21ed25c779fc6e714bc1bcc">   46</a></span>&#160;      <span class="keywordtype">double</span> <a class="code" href="structcutlass_1_1Distribution.html#a846430e3a21ed25c779fc6e714bc1bcc">min</a>;</div><div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#a824641fd3addfa360999614970adfac0">   47</a></span>&#160;      <span class="keywordtype">double</span> <a class="code" href="structcutlass_1_1Distribution.html#a824641fd3addfa360999614970adfac0">max</a>;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;    } <a class="code" href="structcutlass_1_1Distribution.html#afc30b6976acb39e54f061af1bf2870db">uniform</a>;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;    <span class="keyword">struct </span>{</div><div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#a776df53c7ad1b7de983c9f9d17d7438c">   52</a></span>&#160;      <span class="keywordtype">double</span> <a class="code" href="structcutlass_1_1Distribution.html#a776df53c7ad1b7de983c9f9d17d7438c">mean</a>;</div><div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#aee3bd32372426422bb02b335704965aa">   53</a></span>&#160;      <span class="keywordtype">double</span> <a class="code" href="structcutlass_1_1Distribution.html#aee3bd32372426422bb02b335704965aa">stddev</a>;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;    } <a class="code" href="structcutlass_1_1Distribution.html#ada9c50671b405fabbb0841a093f809de">gaussian</a>;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;    <span class="keyword">struct </span>{</div><div class="line"><a name="l00058"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#a69408e1ae607e1bf16a9e7fea1d04617">   58</a></span>&#160;      <span class="keywordtype">double</span> <a class="code" href="structcutlass_1_1Distribution.html#a69408e1ae607e1bf16a9e7fea1d04617">start</a>;</div><div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#a77613df810c3f8f68b595599802cedb4">   59</a></span>&#160;      <span class="keywordtype">double</span> <a class="code" href="structcutlass_1_1Distribution.html#a77613df810c3f8f68b595599802cedb4">delta</a>;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    } <a class="code" href="structcutlass_1_1Distribution.html#ab86d975567ef141ff82067b1f41cd3ee">sequential</a>;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  };</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00064"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#a07cb089b346ef06e198f6043128264fb">   64</a></span>&#160;  <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92a">Kind</a> <a class="code" href="structcutlass_1_1Distribution.html#a07cb089b346ef06e198f6043128264fb">kind</a>;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#a676b1d8b87691b4218f6ed308e6adfc1">   67</a></span>&#160;  <span class="keywordtype">int</span> <a class="code" href="structcutlass_1_1Distribution.html#a676b1d8b87691b4218f6ed308e6adfc1">int_scale</a>;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#a40f0b9d0f92199f8a49c931d34dd8c8a">   73</a></span>&#160;  <a class="code" href="structcutlass_1_1Distribution.html#a40f0b9d0f92199f8a49c931d34dd8c8a">Distribution</a>() : kind(<a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa2ff14122c59a823654b84764f68e597b">Invalid</a>), int_scale(0) {}</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#a5ef87d3af6af0a815a56e74645f32991">   76</a></span>&#160;  <a class="code" href="structcutlass_1_1Distribution.html">Distribution</a> &amp;<a class="code" href="structcutlass_1_1Distribution.html#a5ef87d3af6af0a815a56e74645f32991">set_uniform</a>(<span class="keywordtype">double</span> _min, <span class="keywordtype">double</span> _max, <span class="keywordtype">int</span> _int_scale = 0) {</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;    kind = <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa0fad91cf4fcbc8ab015053bea77090a6">Uniform</a>;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;    <a class="code" href="structcutlass_1_1Distribution.html#afc30b6976acb39e54f061af1bf2870db">uniform</a>.min = _min;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;    <a class="code" href="structcutlass_1_1Distribution.html#afc30b6976acb39e54f061af1bf2870db">uniform</a>.max = _max;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;    int_scale = _int_scale;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  }</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#ad594b5ec1d577e8ef03d4d808a8220b1">   85</a></span>&#160;  <a class="code" href="structcutlass_1_1Distribution.html">Distribution</a> &amp;<a class="code" href="structcutlass_1_1Distribution.html#ad594b5ec1d577e8ef03d4d808a8220b1">set_gaussian</a>(<span class="keywordtype">double</span> _mean, <span class="keywordtype">double</span> _stddev, <span class="keywordtype">int</span> _int_scale = 0) {</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    kind = <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa39890d8be86d514207259b1b5dca3ed5">Gaussian</a>;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    <a class="code" href="structcutlass_1_1Distribution.html#ada9c50671b405fabbb0841a093f809de">gaussian</a>.mean = _mean;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    <a class="code" href="structcutlass_1_1Distribution.html#ada9c50671b405fabbb0841a093f809de">gaussian</a>.stddev = _stddev;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    int_scale = _int_scale;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;  }</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#aad2cf02af3d520544d89843cc4295858">   94</a></span>&#160;  <a class="code" href="structcutlass_1_1Distribution.html">Distribution</a> &amp;<a class="code" href="structcutlass_1_1Distribution.html#aad2cf02af3d520544d89843cc4295858">set_identity</a>() {</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;    kind = <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa08adbc111dc827d524a5509c948d8ba6">Identity</a>;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;  }</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div><div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="structcutlass_1_1Distribution.html#a7fb9689c8ae17d5c72c7d0376fa93767">  100</a></span>&#160;  <a class="code" href="structcutlass_1_1Distribution.html">Distribution</a> &amp;<a class="code" href="structcutlass_1_1Distribution.html#a7fb9689c8ae17d5c72c7d0376fa93767">set_sequential</a>(<span class="keywordtype">double</span> <a class="code" href="structcutlass_1_1Distribution.html#a69408e1ae607e1bf16a9e7fea1d04617">start</a>, <span class="keywordtype">double</span> <a class="code" href="structcutlass_1_1Distribution.html#a77613df810c3f8f68b595599802cedb4">delta</a>, <span class="keywordtype">int</span> _int_scale = 0) {</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;    kind = <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa39d3cf55e90573c8d1dfb483cfb410dc">Sequential</a>;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;    <a class="code" href="structcutlass_1_1Distribution.html#ab86d975567ef141ff82067b1f41cd3ee">sequential</a>.start = <a class="code" href="structcutlass_1_1Distribution.html#a69408e1ae607e1bf16a9e7fea1d04617">start</a>;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;    <a class="code" href="structcutlass_1_1Distribution.html#ab86d975567ef141ff82067b1f41cd3ee">sequential</a>.delta = <a class="code" href="structcutlass_1_1Distribution.html#a77613df810c3f8f68b595599802cedb4">delta</a>;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    int_scale = _int_scale;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;  }</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;};</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;}  <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div><div class="line"><a name="l00114"></a><span class="lineno"><a class="line" href="distribution_8h.html#ab37b35aee616aadf13c6921f5a92e3d0">  114</a></span>&#160;<span class="keyword">inline</span> std::ostream &amp;<a class="code" href="namespacecutlass.html#addd443fc82f2acf867a61acff6c1cd29">operator&lt;&lt;</a>(std::ostream &amp;out, <a class="code" href="structcutlass_1_1Distribution.html">cutlass::Distribution</a> <span class="keyword">const</span> &amp;dist) {</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;  <span class="keywordflow">switch</span> (dist.<a class="code" href="structcutlass_1_1Distribution.html#a07cb089b346ef06e198f6043128264fb">kind</a>) {</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;    <span class="keywordflow">case</span> <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa0fad91cf4fcbc8ab015053bea77090a6">cutlass::Distribution::Uniform</a>:</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;      out &lt;&lt; <span class="stringliteral">&quot;uniform, min: &quot;</span> &lt;&lt; dist.<a class="code" href="structcutlass_1_1Distribution.html#afc30b6976acb39e54f061af1bf2870db">uniform</a>.min &lt;&lt; <span class="stringliteral">&quot;, max: &quot;</span> &lt;&lt; dist.<a class="code" href="structcutlass_1_1Distribution.html#afc30b6976acb39e54f061af1bf2870db">uniform</a>.max;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;      <span class="keywordflow">break</span>;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;    <span class="keywordflow">case</span> <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa39890d8be86d514207259b1b5dca3ed5">cutlass::Distribution::Gaussian</a>:</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;      out &lt;&lt; <span class="stringliteral">&quot;gaussian, mean: &quot;</span> &lt;&lt; dist.<a class="code" href="structcutlass_1_1Distribution.html#ada9c50671b405fabbb0841a093f809de">gaussian</a>.mean &lt;&lt; <span class="stringliteral">&quot;, stddev: &quot;</span> &lt;&lt; dist.<a class="code" href="structcutlass_1_1Distribution.html#ada9c50671b405fabbb0841a093f809de">gaussian</a>.stddev;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;      <span class="keywordflow">break</span>;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;    <span class="keywordflow">case</span> <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa08adbc111dc827d524a5509c948d8ba6">cutlass::Distribution::Identity</a>:</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;      out &lt;&lt; <span class="stringliteral">&quot;identity&quot;</span>;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;      <span class="keywordflow">break</span>;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    <span class="keywordflow">case</span> <a class="code" href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa39d3cf55e90573c8d1dfb483cfb410dc">cutlass::Distribution::Sequential</a>:</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;      out &lt;&lt; <span class="stringliteral">&quot;sequential&quot;</span>;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;      <span class="keywordflow">break</span>;</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    <span class="keywordflow">default</span>:</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;      out &lt;&lt; <span class="stringliteral">&quot;unknown&quot;</span>;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  }</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  out &lt;&lt; <span class="stringliteral">&quot;, int_scale: &quot;</span> &lt;&lt; dist.<a class="code" href="structcutlass_1_1Distribution.html#a676b1d8b87691b4218f6ed308e6adfc1">int_scale</a>;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  <span class="keywordflow">return</span> out;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;}</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div><div class="ttc" id="structcutlass_1_1Distribution_html_a40f0b9d0f92199f8a49c931d34dd8c8a"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a40f0b9d0f92199f8a49c931d34dd8c8a">cutlass::Distribution::Distribution</a></div><div class="ttdeci">Distribution()</div><div class="ttdef"><b>Definition:</b> distribution.h:73</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a499f4023e0d42356ce71d38cc32bf92aa0fad91cf4fcbc8ab015053bea77090a6"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa0fad91cf4fcbc8ab015053bea77090a6">cutlass::Distribution::Uniform</a></div><div class="ttdef"><b>Definition:</b> distribution.h:40</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a499f4023e0d42356ce71d38cc32bf92aa39890d8be86d514207259b1b5dca3ed5"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa39890d8be86d514207259b1b5dca3ed5">cutlass::Distribution::Gaussian</a></div><div class="ttdef"><b>Definition:</b> distribution.h:40</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_afc30b6976acb39e54f061af1bf2870db"><div class="ttname"><a href="structcutlass_1_1Distribution.html#afc30b6976acb39e54f061af1bf2870db">cutlass::Distribution::uniform</a></div><div class="ttdeci">struct cutlass::Distribution::@18::@20 uniform</div><div class="ttdoc">Uniform distribution. </div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a776df53c7ad1b7de983c9f9d17d7438c"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a776df53c7ad1b7de983c9f9d17d7438c">cutlass::Distribution::mean</a></div><div class="ttdeci">double mean</div><div class="ttdef"><b>Definition:</b> distribution.h:52</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a07cb089b346ef06e198f6043128264fb"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a07cb089b346ef06e198f6043128264fb">cutlass::Distribution::kind</a></div><div class="ttdeci">Kind kind</div><div class="ttdoc">Active variant kind. </div><div class="ttdef"><b>Definition:</b> distribution.h:64</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_ada9c50671b405fabbb0841a093f809de"><div class="ttname"><a href="structcutlass_1_1Distribution.html#ada9c50671b405fabbb0841a093f809de">cutlass::Distribution::gaussian</a></div><div class="ttdeci">struct cutlass::Distribution::@18::@21 gaussian</div><div class="ttdoc">Gaussian distribution. </div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_aad2cf02af3d520544d89843cc4295858"><div class="ttname"><a href="structcutlass_1_1Distribution.html#aad2cf02af3d520544d89843cc4295858">cutlass::Distribution::set_identity</a></div><div class="ttdeci">Distribution &amp; set_identity()</div><div class="ttdoc">Sets identity. </div><div class="ttdef"><b>Definition:</b> distribution.h:94</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a499f4023e0d42356ce71d38cc32bf92a"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92a">cutlass::Distribution::Kind</a></div><div class="ttdeci">Kind</div><div class="ttdoc">Variant types. </div><div class="ttdef"><b>Definition:</b> distribution.h:40</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a824641fd3addfa360999614970adfac0"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a824641fd3addfa360999614970adfac0">cutlass::Distribution::max</a></div><div class="ttdeci">double max</div><div class="ttdef"><b>Definition:</b> distribution.h:47</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_aee3bd32372426422bb02b335704965aa"><div class="ttname"><a href="structcutlass_1_1Distribution.html#aee3bd32372426422bb02b335704965aa">cutlass::Distribution::stddev</a></div><div class="ttdeci">double stddev</div><div class="ttdef"><b>Definition:</b> distribution.h:53</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a499f4023e0d42356ce71d38cc32bf92aa08adbc111dc827d524a5509c948d8ba6"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa08adbc111dc827d524a5509c948d8ba6">cutlass::Distribution::Identity</a></div><div class="ttdef"><b>Definition:</b> distribution.h:40</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a846430e3a21ed25c779fc6e714bc1bcc"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a846430e3a21ed25c779fc6e714bc1bcc">cutlass::Distribution::min</a></div><div class="ttdeci">double min</div><div class="ttdef"><b>Definition:</b> distribution.h:46</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a5ef87d3af6af0a815a56e74645f32991"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a5ef87d3af6af0a815a56e74645f32991">cutlass::Distribution::set_uniform</a></div><div class="ttdeci">Distribution &amp; set_uniform(double _min, double _max, int _int_scale=0)</div><div class="ttdoc">Configures distribution as uniform random. </div><div class="ttdef"><b>Definition:</b> distribution.h:76</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a69408e1ae607e1bf16a9e7fea1d04617"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a69408e1ae607e1bf16a9e7fea1d04617">cutlass::Distribution::start</a></div><div class="ttdeci">double start</div><div class="ttdef"><b>Definition:</b> distribution.h:58</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a77613df810c3f8f68b595599802cedb4"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a77613df810c3f8f68b595599802cedb4">cutlass::Distribution::delta</a></div><div class="ttdeci">double delta</div><div class="ttdef"><b>Definition:</b> distribution.h:59</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a499f4023e0d42356ce71d38cc32bf92aa39d3cf55e90573c8d1dfb483cfb410dc"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa39d3cf55e90573c8d1dfb483cfb410dc">cutlass::Distribution::Sequential</a></div><div class="ttdef"><b>Definition:</b> distribution.h:40</div></div>
<div class="ttc" id="namespacecutlass_html_addd443fc82f2acf867a61acff6c1cd29"><div class="ttname"><a href="namespacecutlass.html#addd443fc82f2acf867a61acff6c1cd29">cutlass::operator&lt;&lt;</a></div><div class="ttdeci">std::ostream &amp; operator&lt;&lt;(std::ostream &amp;out, complex&lt; T &gt; const &amp;z)</div><div class="ttdef"><b>Definition:</b> complex.h:291</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html"><div class="ttname"><a href="structcutlass_1_1Distribution.html">cutlass::Distribution</a></div><div class="ttdoc">Distribution type. </div><div class="ttdef"><b>Definition:</b> distribution.h:38</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_ab86d975567ef141ff82067b1f41cd3ee"><div class="ttname"><a href="structcutlass_1_1Distribution.html#ab86d975567ef141ff82067b1f41cd3ee">cutlass::Distribution::sequential</a></div><div class="ttdeci">struct cutlass::Distribution::@18::@22 sequential</div><div class="ttdoc">Elements are linear combination of row and column index. </div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a676b1d8b87691b4218f6ed308e6adfc1"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a676b1d8b87691b4218f6ed308e6adfc1">cutlass::Distribution::int_scale</a></div><div class="ttdeci">int int_scale</div><div class="ttdoc">Random values are cast to integer after scaling by this power of two. </div><div class="ttdef"><b>Definition:</b> distribution.h:67</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a7fb9689c8ae17d5c72c7d0376fa93767"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a7fb9689c8ae17d5c72c7d0376fa93767">cutlass::Distribution::set_sequential</a></div><div class="ttdeci">Distribution &amp; set_sequential(double start, double delta, int _int_scale=0)</div><div class="ttdoc">Sets sequential. </div><div class="ttdef"><b>Definition:</b> distribution.h:100</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_a499f4023e0d42356ce71d38cc32bf92aa2ff14122c59a823654b84764f68e597b"><div class="ttname"><a href="structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa2ff14122c59a823654b84764f68e597b">cutlass::Distribution::Invalid</a></div><div class="ttdef"><b>Definition:</b> distribution.h:40</div></div>
<div class="ttc" id="structcutlass_1_1Distribution_html_ad594b5ec1d577e8ef03d4d808a8220b1"><div class="ttname"><a href="structcutlass_1_1Distribution.html#ad594b5ec1d577e8ef03d4d808a8220b1">cutlass::Distribution::set_gaussian</a></div><div class="ttdeci">Distribution &amp; set_gaussian(double _mean, double _stddev, int _int_scale=0)</div><div class="ttdoc">Configures distribution as Gaussian distribution. </div><div class="ttdef"><b>Definition:</b> distribution.h:85</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
