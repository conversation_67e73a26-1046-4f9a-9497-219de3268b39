<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: epilogue_base.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_05a6795d99d74f63b7300fc6eb9e55c2.html">threadblock</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">epilogue_base.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="epilogue__base_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &lt;assert.h&gt;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="matrix__shape_8h.html">cutlass/matrix_shape.h</a>&quot;</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="vector_8h.html">cutlass/layout/vector.h</a>&quot;</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor_8h.html">cutlass/layout/tensor.h</a>&quot;</span></div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__coord_8h.html">cutlass/tensor_coord.h</a>&quot;</span></div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="aligned__buffer_8h.html">cutlass/aligned_buffer.h</a>&quot;</span></div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="include_2cutlass_2gemm_2gemm_8h.html">cutlass/gemm/gemm.h</a>&quot;</span></div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="pitch__linear__thread__map_8h.html">cutlass/transform/pitch_linear_thread_map.h</a>&quot;</span></div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="keyword">namespace </span>threadblock {</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;  <span class="keyword">typename</span> Shape_,                          </div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  <span class="keyword">typename</span> WarpMmaOperator_,                </div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keywordtype">int</span> PartitionsK,                          </div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="keyword">typename</span> AccumulatorFragmentIterator_,    </div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;  <span class="keyword">typename</span> WarpTileIterator_,               </div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  <span class="keyword">typename</span> Padding_                         </div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;&gt;</div><div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html">   67</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html">EpilogueBase</a> {</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div><div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a7d4571b5dae2f9e423d2385b35d17c7e">   70</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a7d4571b5dae2f9e423d2385b35d17c7e">Shape</a> = Shape_;</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a9da20f1143795486959963b8b3b3a794">   71</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a9da20f1143795486959963b8b3b3a794">WarpMmaOperator</a> = WarpMmaOperator_;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a980ad531ed3e93a988ccfc9ac341dc4d">   72</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a980ad531ed3e93a988ccfc9ac341dc4d">kPartitionsK</a> = PartitionsK;</div><div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#aa39c8ae2394f8e9514c124e1d16ec106">   73</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#aa39c8ae2394f8e9514c124e1d16ec106">AccumulatorFragmentIterator</a> = AccumulatorFragmentIterator_;</div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a8f1f19208ad8cf5355a9b6a33bcda7f5">   74</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a8f1f19208ad8cf5355a9b6a33bcda7f5">WarpTileIterator</a> = WarpTileIterator_;</div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a18d185fd1a896120f0ceb22c83758635">   75</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a18d185fd1a896120f0ceb22c83758635">Padding</a> = Padding_;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#ac3b3149663228d308b5cec15950b652e">   78</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a1707fb90363342996902b96ccd3bb176">   81</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a1707fb90363342996902b96ccd3bb176">AccumulatorTile</a> = <span class="keyword">typename</span> AccumulatorFragmentIterator::AccumulatorTile;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a2ae6385ff74559cbe4dcb16c4470cdfc">   84</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a2ae6385ff74559cbe4dcb16c4470cdfc">ElementAccumulator</a> = <span class="keyword">typename</span> AccumulatorTile::Element;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  </div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">WarpCount</a> = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape</a>&lt;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    Shape::kM / WarpMmaOperator::Shape::kM,</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    Shape::kN / WarpMmaOperator::Shape::kN,</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    kPartitionsK</div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a4549c93a2946f616273e095a230e37bc">   92</a></span>&#160;  &gt;;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html">   97</a></span>&#160;  <span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html">SharedStorage</a> {</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;    </div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;    <span class="comment">// Type definitions</span></div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div><div class="line"><a name="l00104"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#ac20a9f25cf5e6e4a93d9eea5297f9e8b">  104</a></span>&#160;    <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#ac20a9f25cf5e6e4a93d9eea5297f9e8b">Element</a> = <span class="keyword">typename</span> WarpTileIterator::Element;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div><div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a14e2c2be1d55b6818f9a8f89c286b051">  107</a></span>&#160;    <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a14e2c2be1d55b6818f9a8f89c286b051">TensorRef</a> = <span class="keyword">typename</span> WarpTileIterator::TensorRef;</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a702ca51bb3a780cefa51fd28028b65b1">  110</a></span>&#160;    <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a702ca51bb3a780cefa51fd28028b65b1">Layout</a> = <span class="keyword">typename</span> WarpTileIterator::Layout;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;    </div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">Shape</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;      <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a7a47fe0c44571a0a68a43c5a47cf676a">WarpCount::kM</a> * WarpTileIterator::Shape::kRow * <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a8b7ab79699355caf179323b7ad4c71fc">WarpCount::kK</a>,</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;      <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a9fcbaa4b47b83d0c8a09979ad5c98a1e">WarpCount::kN</a> * WarpTileIterator::Shape::kColumn</div><div class="line"><a name="l00116"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a3a8e52453dfb660cd9550bb6611d1035">  116</a></span>&#160;    &gt;;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;    <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">StorageShape</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;      <a class="code" href="structcutlass_1_1MatrixShape.html#a6e376c7fd5954ab6040fea695ae8a889">Shape::kRow</a> + Padding::kRow, </div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;      <a class="code" href="structcutlass_1_1MatrixShape.html#a160927c2ddf7239f5be8c7cf6374a82e">Shape::kColumn</a> + Padding::kColumn</div><div class="line"><a name="l00122"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a997d00c5a25c737bdb9364956e230f18">  122</a></span>&#160;    &gt;;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    <span class="comment">// Data members</span></div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div><div class="line"><a name="l00128"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#ab44f6ca919320c430087b7103541d77a">  128</a></span>&#160;    <a class="code" href="structcutlass_1_1AlignedBuffer.html">AlignedBuffer&lt;Element, StorageShape::kCount&gt;</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#ab44f6ca919320c430087b7103541d77a">storage</a>;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    <span class="comment">// Methods</span></div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    CUTLASS_DEVICE</div><div class="line"><a name="l00136"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a2d57be4f0bdad670c7eb67e64dd1a9f5">  136</a></span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#ac20a9f25cf5e6e4a93d9eea5297f9e8b">Element</a> *<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a2d57be4f0bdad670c7eb67e64dd1a9f5">data</a>() {</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;      <span class="keywordflow">return</span> storage.<a class="code" href="structcutlass_1_1AlignedBuffer.html#a8ed8b9d3469621fc82d0041846c59da2">data</a>();</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;    }</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    CUTLASS_DEVICE</div><div class="line"><a name="l00142"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a99b724aec92bebece9bc187fb636bc11">  142</a></span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a14e2c2be1d55b6818f9a8f89c286b051">TensorRef</a> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a99b724aec92bebece9bc187fb636bc11">reference</a>() {</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;      <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a14e2c2be1d55b6818f9a8f89c286b051">TensorRef</a>(</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        storage.<a class="code" href="structcutlass_1_1AlignedBuffer.html#a8ed8b9d3469621fc82d0041846c59da2">data</a>(), </div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html#a770edcc93145fc3dfa4dfdf37a7c515e">Layout::packed</a>({<a class="code" href="structcutlass_1_1MatrixShape.html#a6e376c7fd5954ab6040fea695ae8a889">StorageShape::kRow</a>, <a class="code" href="structcutlass_1_1MatrixShape.html#a160927c2ddf7239f5be8c7cf6374a82e">StorageShape::kColumn</a>}));</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;    }</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    CUTLASS_DEVICE</div><div class="line"><a name="l00149"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#afd521c2dc754bb30024e8767bfc51e49">  149</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#afd521c2dc754bb30024e8767bfc51e49">debug_print</a>() {</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;      <span class="keywordflow">if</span> (threadIdx.x == 0) {</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;<span class="preprocessor">        #pragma unroll 1</span></div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> r = 0; r &lt; <a class="code" href="structcutlass_1_1MatrixShape.html#a6e376c7fd5954ab6040fea695ae8a889">Shape::kRow</a>; ++r) {</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;<span class="preprocessor">          #pragma unroll 1</span></div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;          <span class="keywordflow">for</span> (<span class="keywordtype">int</span> c = 0; c &lt; <a class="code" href="structcutlass_1_1MatrixShape.html#a160927c2ddf7239f5be8c7cf6374a82e">Shape::kColumn</a>; ++c) {</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;            printf(<span class="stringliteral">&quot;%d  &quot;</span>, <span class="keywordtype">int</span>(storage.<a class="code" href="structcutlass_1_1AlignedBuffer.html#a8ed8b9d3469621fc82d0041846c59da2">data</a>()[r * <a class="code" href="structcutlass_1_1MatrixShape.html#a160927c2ddf7239f5be8c7cf6374a82e">StorageShape::kColumn</a> + c]));</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;          }</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;          printf(<span class="stringliteral">&quot;\n&quot;</span>);</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;        }</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;      }</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;      __syncthreads();</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    }</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;  };</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;<span class="keyword">protected</span>:</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;</div><div class="line"><a name="l00173"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#aa086274af03133d2c35c27f5e76e609e">  173</a></span>&#160;  <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html">SharedStorage</a> &amp;<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#aa086274af03133d2c35c27f5e76e609e">shared_storage_</a>;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;</div><div class="line"><a name="l00176"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#ac858903691c5e2b52c4a470e712911e3">  176</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a8f1f19208ad8cf5355a9b6a33bcda7f5">WarpTileIterator</a> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#ac858903691c5e2b52c4a470e712911e3">warp_tile_iterator_</a>;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00182"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#ac7ce30b261c1d1f9891e05a20850fa6d">  182</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#ac7ce30b261c1d1f9891e05a20850fa6d">EpilogueBase</a>(</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;    <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html">SharedStorage</a> &amp;shared_storage,    </div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    <span class="keywordtype">int</span> thread_idx,                   </div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;    <span class="keywordtype">int</span> warp_idx,                     </div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;    <span class="keywordtype">int</span> lane_idx                      </div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;  ):</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;    shared_storage_(shared_storage),</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;    warp_tile_iterator_(shared_storage.<a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a99b724aec92bebece9bc187fb636bc11">reference</a>(), lane_idx) {</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;    <span class="comment">// Compute warp location within threadblock tile by mapping the warp_id to three coordinates:</span></div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;    <span class="comment">//   _m: the warp&#39;s position within the threadblock along the M dimension</span></div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;    <span class="comment">//   _n: the warp&#39;s position within the threadblock along the N dimension</span></div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;    <span class="comment">//   _k: the warp&#39;s position within the threadblock along the K dimension</span></div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;    <span class="keywordtype">int</span> warp_k = warp_idx / (<a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a7a47fe0c44571a0a68a43c5a47cf676a">WarpCount::kM</a> * <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a9fcbaa4b47b83d0c8a09979ad5c98a1e">WarpCount::kN</a>);</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;    <span class="keywordtype">int</span> warp_mn = warp_idx % (<a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a7a47fe0c44571a0a68a43c5a47cf676a">WarpCount::kM</a> * <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a9fcbaa4b47b83d0c8a09979ad5c98a1e">WarpCount::kN</a>);</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;    <span class="keywordtype">int</span> warp_m = warp_mn % <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a7a47fe0c44571a0a68a43c5a47cf676a">WarpCount::kM</a>;</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;    <span class="keywordtype">int</span> warp_n = warp_mn / <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a7a47fe0c44571a0a68a43c5a47cf676a">WarpCount::kM</a>;</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;    <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> warp_offset{warp_k * <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html#a7a47fe0c44571a0a68a43c5a47cf676a">WarpCount::kM</a> + warp_m, warp_n};</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    warp_tile_iterator_.add_tile_offset(warp_offset);</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;  }</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;};</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;} <span class="comment">// namespace threadblock</span></div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div><div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html_a7a47fe0c44571a0a68a43c5a47cf676a"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html#a7a47fe0c44571a0a68a43c5a47cf676a">cutlass::gemm::GemmShape::kM</a></div><div class="ttdeci">static int const kM</div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:58</div></div>
<div class="ttc" id="structcutlass_1_1MatrixShape_html"><div class="ttname"><a href="structcutlass_1_1MatrixShape.html">cutlass::MatrixShape</a></div><div class="ttdoc">Describes the size of a matrix tile. </div><div class="ttdef"><b>Definition:</b> matrix_shape.h:42</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage_html_afd521c2dc754bb30024e8767bfc51e49"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#afd521c2dc754bb30024e8767bfc51e49">cutlass::epilogue::threadblock::EpilogueBase::SharedStorage::debug_print</a></div><div class="ttdeci">CUTLASS_DEVICE void debug_print()</div><div class="ttdef"><b>Definition:</b> epilogue_base.h:149</div></div>
<div class="ttc" id="structcutlass_1_1MatrixShape_html_a160927c2ddf7239f5be8c7cf6374a82e"><div class="ttname"><a href="structcutlass_1_1MatrixShape.html#a160927c2ddf7239f5be8c7cf6374a82e">cutlass::MatrixShape::kColumn</a></div><div class="ttdeci">static int const kColumn</div><div class="ttdoc">columns of a matrix </div><div class="ttdef"><b>Definition:</b> matrix_shape.h:44</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_ac858903691c5e2b52c4a470e712911e3"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#ac858903691c5e2b52c4a470e712911e3">cutlass::epilogue::threadblock::EpilogueBase::warp_tile_iterator_</a></div><div class="ttdeci">WarpTileIterator warp_tile_iterator_</div><div class="ttdoc">Stores a warp&amp;#39;s fragment of accumulators to SMEM. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:176</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_aa086274af03133d2c35c27f5e76e609e"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#aa086274af03133d2c35c27f5e76e609e">cutlass::epilogue::threadblock::EpilogueBase::shared_storage_</a></div><div class="ttdeci">SharedStorage &amp; shared_storage_</div><div class="ttdef"><b>Definition:</b> epilogue_base.h:173</div></div>
<div class="ttc" id="pitch__linear__thread__map_8h_html"><div class="ttname"><a href="pitch__linear__thread__map_8h.html">pitch_linear_thread_map.h</a></div><div class="ttdoc">Templates implementing how threads are mapped to a given tile. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_a9da20f1143795486959963b8b3b3a794"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a9da20f1143795486959963b8b3b3a794">cutlass::epilogue::threadblock::EpilogueBase::WarpMmaOperator</a></div><div class="ttdeci">WarpMmaOperator_ WarpMmaOperator</div><div class="ttdef"><b>Definition:</b> epilogue_base.h:71</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html">cutlass::epilogue::threadblock::EpilogueBase::SharedStorage</a></div><div class="ttdoc">Shared storage allocation needed by the epilogue. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:97</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage_html_a2d57be4f0bdad670c7eb67e64dd1a9f5"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a2d57be4f0bdad670c7eb67e64dd1a9f5">cutlass::epilogue::threadblock::EpilogueBase::SharedStorage::data</a></div><div class="ttdeci">CUTLASS_DEVICE Element * data()</div><div class="ttdoc">Returns a pointer to the shared memory buffer. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:136</div></div>
<div class="ttc" id="include_2cutlass_2gemm_2gemm_8h_html"><div class="ttname"><a href="include_2cutlass_2gemm_2gemm_8h.html">gemm.h</a></div><div class="ttdoc">Defines common types used for all GEMM-like operators. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_a2ae6385ff74559cbe4dcb16c4470cdfc"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a2ae6385ff74559cbe4dcb16c4470cdfc">cutlass::epilogue::threadblock::EpilogueBase::ElementAccumulator</a></div><div class="ttdeci">typename AccumulatorTile::Element ElementAccumulator</div><div class="ttdoc">Accumulator element. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:84</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage_html_a14e2c2be1d55b6818f9a8f89c286b051"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a14e2c2be1d55b6818f9a8f89c286b051">cutlass::epilogue::threadblock::EpilogueBase::SharedStorage::TensorRef</a></div><div class="ttdeci">typename WarpTileIterator::TensorRef TensorRef</div><div class="ttdoc">Tensor reference to shared memory allocation. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:107</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html_a8b7ab79699355caf179323b7ad4c71fc"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html#a8b7ab79699355caf179323b7ad4c71fc">cutlass::gemm::GemmShape::kK</a></div><div class="ttdeci">static int const kK</div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:60</div></div>
<div class="ttc" id="tensor_8h_html"><div class="ttname"><a href="tensor_8h.html">tensor.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes for common 4-D and 5-D tensor formats...</div></div>
<div class="ttc" id="matrix__shape_8h_html"><div class="ttname"><a href="matrix__shape_8h.html">matrix_shape.h</a></div><div class="ttdoc">Defines a Shape template for matrix tiles. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_a980ad531ed3e93a988ccfc9ac341dc4d"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a980ad531ed3e93a988ccfc9ac341dc4d">cutlass::epilogue::threadblock::EpilogueBase::kPartitionsK</a></div><div class="ttdeci">static int const kPartitionsK</div><div class="ttdef"><b>Definition:</b> epilogue_base.h:72</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage_html_ac20a9f25cf5e6e4a93d9eea5297f9e8b"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#ac20a9f25cf5e6e4a93d9eea5297f9e8b">cutlass::epilogue::threadblock::EpilogueBase::SharedStorage::Element</a></div><div class="ttdeci">typename WarpTileIterator::Element Element</div><div class="ttdoc">Element type of shared memory. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:104</div></div>
<div class="ttc" id="tensor__coord_8h_html"><div class="ttname"><a href="tensor__coord_8h.html">tensor_coord.h</a></div><div class="ttdoc">Defines a canonical coordinate for rank=4 tensors offering named indices. </div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage_html_ab44f6ca919320c430087b7103541d77a"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#ab44f6ca919320c430087b7103541d77a">cutlass::epilogue::threadblock::EpilogueBase::SharedStorage::storage</a></div><div class="ttdeci">AlignedBuffer&lt; Element, StorageShape::kCount &gt; storage</div><div class="ttdef"><b>Definition:</b> epilogue_base.h:128</div></div>
<div class="ttc" id="structcutlass_1_1MatrixShape_html_a6e376c7fd5954ab6040fea695ae8a889"><div class="ttname"><a href="structcutlass_1_1MatrixShape.html#a6e376c7fd5954ab6040fea695ae8a889">cutlass::MatrixShape::kRow</a></div><div class="ttdeci">static int const kRow</div><div class="ttdoc">rows of a matrix </div><div class="ttdef"><b>Definition:</b> matrix_shape.h:43</div></div>
<div class="ttc" id="aligned__buffer_8h_html"><div class="ttname"><a href="aligned__buffer_8h.html">aligned_buffer.h</a></div><div class="ttdoc">AlignedBuffer is a container for trivially copyable elements suitable for use in unions and shared me...</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html">cutlass::AlignedBuffer</a></div><div class="ttdoc">Modifies semantics of cutlass::Array&lt;&gt; to provide guaranteed alignment. </div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:45</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape</a></div><div class="ttdoc">Shape of a matrix multiply-add operation. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:57</div></div>
<div class="ttc" id="structcutlass_1_1AlignedBuffer_html_a8ed8b9d3469621fc82d0041846c59da2"><div class="ttname"><a href="structcutlass_1_1AlignedBuffer.html#a8ed8b9d3469621fc82d0041846c59da2">cutlass::AlignedBuffer::data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE pointer data()</div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:84</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage_html_a702ca51bb3a780cefa51fd28028b65b1"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a702ca51bb3a780cefa51fd28028b65b1">cutlass::epilogue::threadblock::EpilogueBase::SharedStorage::Layout</a></div><div class="ttdeci">typename WarpTileIterator::Layout Layout</div><div class="ttdoc">Layout of shared memory allocation. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:110</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_aa39c8ae2394f8e9514c124e1d16ec106"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#aa39c8ae2394f8e9514c124e1d16ec106">cutlass::epilogue::threadblock::EpilogueBase::AccumulatorFragmentIterator</a></div><div class="ttdeci">AccumulatorFragmentIterator_ AccumulatorFragmentIterator</div><div class="ttdef"><b>Definition:</b> epilogue_base.h:73</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="vector_8h_html"><div class="ttname"><a href="vector_8h.html">vector.h</a></div><div class="ttdoc">Defines layout functions used for rank=1 vectors. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_a7d4571b5dae2f9e423d2385b35d17c7e"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a7d4571b5dae2f9e423d2385b35d17c7e">cutlass::epilogue::threadblock::EpilogueBase::Shape</a></div><div class="ttdeci">Shape_ Shape</div><div class="ttdef"><b>Definition:</b> epilogue_base.h:70</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html">cutlass::epilogue::threadblock::EpilogueBase</a></div><div class="ttdoc">Base class for epilogues defining warp-level. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:67</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html_a770edcc93145fc3dfa4dfdf37a7c515e"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html#a770edcc93145fc3dfa4dfdf37a7c515e">cutlass::layout::RowMajor::packed</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE RowMajor packed(MatrixCoord const &amp;extent)</div><div class="ttdoc">Helper returns a layout to a tightly packed tensor. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:93</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_a18d185fd1a896120f0ceb22c83758635"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a18d185fd1a896120f0ceb22c83758635">cutlass::epilogue::threadblock::EpilogueBase::Padding</a></div><div class="ttdeci">Padding_ Padding</div><div class="ttdef"><b>Definition:</b> epilogue_base.h:75</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_ac7ce30b261c1d1f9891e05a20850fa6d"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#ac7ce30b261c1d1f9891e05a20850fa6d">cutlass::epilogue::threadblock::EpilogueBase::EpilogueBase</a></div><div class="ttdeci">CUTLASS_DEVICE EpilogueBase(SharedStorage &amp;shared_storage, int thread_idx, int warp_idx, int lane_idx)</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:182</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_a8f1f19208ad8cf5355a9b6a33bcda7f5"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a8f1f19208ad8cf5355a9b6a33bcda7f5">cutlass::epilogue::threadblock::EpilogueBase::WarpTileIterator</a></div><div class="ttdeci">WarpTileIterator_ WarpTileIterator</div><div class="ttdef"><b>Definition:</b> epilogue_base.h:74</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_a1707fb90363342996902b96ccd3bb176"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a1707fb90363342996902b96ccd3bb176">cutlass::epilogue::threadblock::EpilogueBase::AccumulatorTile</a></div><div class="ttdeci">typename AccumulatorFragmentIterator::AccumulatorTile AccumulatorTile</div><div class="ttdoc">The complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:81</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html">cutlass::MatrixCoord</a></div><div class="ttdef"><b>Definition:</b> matrix_coord.h:39</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage_html_a99b724aec92bebece9bc187fb636bc11"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html#a99b724aec92bebece9bc187fb636bc11">cutlass::epilogue::threadblock::EpilogueBase::SharedStorage::reference</a></div><div class="ttdeci">CUTLASS_DEVICE TensorRef reference()</div><div class="ttdoc">Returns a tensor reference to the shared memory buffer. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:142</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html_a9fcbaa4b47b83d0c8a09979ad5c98a1e"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html#a9fcbaa4b47b83d0c8a09979ad5c98a1e">cutlass::gemm::GemmShape::kN</a></div><div class="ttdeci">static int const kN</div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:59</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
