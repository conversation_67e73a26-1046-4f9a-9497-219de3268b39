<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: epilogue.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_05a6795d99d74f63b7300fc6eb9e55c2.html">threadblock</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">epilogue.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="epilogue_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &lt;assert.h&gt;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="vector_8h.html">cutlass/layout/vector.h</a>&quot;</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor_8h.html">cutlass/layout/tensor.h</a>&quot;</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__coord_8h.html">cutlass/tensor_coord.h</a>&quot;</span></div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="aligned__buffer_8h.html">cutlass/aligned_buffer.h</a>&quot;</span></div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="functional_8h.html">cutlass/functional.h</a>&quot;</span></div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="include_2cutlass_2gemm_2gemm_8h.html">cutlass/gemm/gemm.h</a>&quot;</span></div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="pitch__linear__thread__map_8h.html">cutlass/transform/pitch_linear_thread_map.h</a>&quot;</span></div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="regular__tile__iterator_8h.html">cutlass/transform/threadblock/regular_tile_iterator.h</a>&quot;</span></div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="epilogue__base_8h.html">cutlass/epilogue/threadblock/epilogue_base.h</a>&quot;</span></div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="epilogue_2threadblock_2predicated__tile__iterator_8h.html">cutlass/epilogue/threadblock/predicated_tile_iterator.h</a>&quot;</span></div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="keyword">namespace </span>threadblock {</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;  <span class="keyword">typename</span> Shape_,                          </div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  <span class="keyword">typename</span> WarpMmaOperator_,                </div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;  <span class="keywordtype">int</span> PartitionsK,                          </div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <span class="keyword">typename</span> OutputTileIterator_,             </div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  <span class="keyword">typename</span> AccumulatorFragmentIterator_,    </div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <span class="keyword">typename</span> WarpTileIterator_,               </div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  <span class="keyword">typename</span> SharedLoadIterator_,             </div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  <span class="keyword">typename</span> OutputOp_,                       </div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  <span class="keyword">typename</span> Padding_                         </div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;&gt;</div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html">   74</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html">Epilogue</a> : </div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="keyword">public</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html">EpilogueBase</a>&lt;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;    Shape_, </div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;    WarpMmaOperator_, </div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;    PartitionsK, </div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;    AccumulatorFragmentIterator_, </div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;    WarpTileIterator_, </div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;    Padding_&gt; {</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html">Base</a> = <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html">EpilogueBase</a>&lt;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    Shape_, </div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    WarpMmaOperator_, </div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    PartitionsK, </div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    AccumulatorFragmentIterator_, </div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    WarpTileIterator_, </div><div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a2d4a8adca40586b504f4e0a7630afa0a">   91</a></span>&#160;    Padding_&gt;;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00093"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ad9918897985656169962aaf48d16f273">   93</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ad9918897985656169962aaf48d16f273">Shape</a> = Shape_;</div><div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a08c18f01a998cfb1eea765abbe9c902e">   94</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a08c18f01a998cfb1eea765abbe9c902e">WarpMmaOperator</a> = WarpMmaOperator_;</div><div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ab08e1b54d71da091b702f28b5741b086">   95</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ab08e1b54d71da091b702f28b5741b086">kPartitionsK</a> = PartitionsK;</div><div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a977dbfb13c48512446e045b709368038">   96</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a977dbfb13c48512446e045b709368038">OutputTileIterator</a> = OutputTileIterator_;</div><div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a0c3840f9e6462afeaa4cff567360912b">   97</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a0c3840f9e6462afeaa4cff567360912b">AccumulatorFragmentIterator</a> = AccumulatorFragmentIterator_;</div><div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a0cc6b8507106d9cd35ea1628adb3398c">   98</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a0cc6b8507106d9cd35ea1628adb3398c">WarpTileIterator</a> = WarpTileIterator_;</div><div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a1dcbb6afb4bafd601e20e77fb241705a">   99</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a1dcbb6afb4bafd601e20e77fb241705a">SharedLoadIterator</a> = SharedLoadIterator_;</div><div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a549eece4fc64291c8f0410dcb20d560a">  100</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a549eece4fc64291c8f0410dcb20d560a">OutputOp</a> = OutputOp_;</div><div class="line"><a name="l00101"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a36970da339d478df9807c01bd26fb87a">  101</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a36970da339d478df9807c01bd26fb87a">Padding</a> = Padding_;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div><div class="line"><a name="l00104"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a7589e814463281fbf6397a7597e3fac1">  104</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a8a5f856300412017f5c12082af70b82f">  105</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a8a5f856300412017f5c12082af70b82f">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html#a4062a36ab044fdea058504ed52ee60b8">Layout::LongIndex</a>;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div><div class="line"><a name="l00108"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a391a932cd8341c5934cc48ec5fa4c0ab">  108</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a391a932cd8341c5934cc48ec5fa4c0ab">AccumulatorTile</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a1707fb90363342996902b96ccd3bb176">Base::AccumulatorTile</a>;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00111"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a3c4101ad87f1e04472c332c012d8b3df">  111</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a3c4101ad87f1e04472c332c012d8b3df">ElementAccumulator</a> = <span class="keyword">typename</span> WarpTileIterator::Element;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;</div><div class="line"><a name="l00115"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ab0570eddc6943f2607762ec49a1ec174">  115</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ab0570eddc6943f2607762ec49a1ec174">ElementOutput</a> = <span class="keyword">typename</span> OutputTileIterator::Element;</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00118"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#af8ecba80487465f25daac670e661627e">  118</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#af8ecba80487465f25daac670e661627e">kElementsPerAccess</a> = OutputTileIterator::kElementsPerAccess;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div><div class="line"><a name="l00121"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a605570e320b4e342d3f7b23263d6ee37">  121</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a605570e320b4e342d3f7b23263d6ee37">TensorRef</a> = <span class="keyword">typename</span> OutputTileIterator::TensorRef;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div><div class="line"><a name="l00124"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a9087b022bc5baed5bdf5e828cff31d48">  124</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a9087b022bc5baed5bdf5e828cff31d48">SyncTensorRef</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html">cutlass::TensorRef&lt;int, cutlass::layout::PackedVectorLayout&gt;</a>;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;</div><div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a92587dbbf9e08f1db3fab5443ae870e8">  127</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a92587dbbf9e08f1db3fab5443ae870e8">ConstTensorRef</a> = <span class="keyword">typename</span> OutputTileIterator::ConstTensorRef;</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a53f99fb2f4a1abd0ad56ce7c6e1bdac2">OutputAccessType</a> = Array&lt;</div><div class="line"><a name="l00131"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a53f99fb2f4a1abd0ad56ce7c6e1bdac2">  131</a></span>&#160;    <span class="keyword">typename</span> OutputTileIterator::Element, OutputTileIterator::kElementsPerAccess&gt;;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00134"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a0cfa64af365e51d50549528edea00692">  134</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a0cfa64af365e51d50549528edea00692">AccumulatorAccessType</a> = Array&lt;typename WarpTileIterator::Element, OutputTileIterator::kElementsPerAccess&gt;; </div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  </div><div class="line"><a name="l00137"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a9b0cbcf46b87ddda5b2024d2facb07ce">  137</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a9b0cbcf46b87ddda5b2024d2facb07ce">WarpCount</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a4549c93a2946f616273e095a230e37bc">Base::WarpCount</a>;</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(SharedLoadIterator::Fragment::kElements == OutputTileIterator::Fragment::kElements,</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <span class="stringliteral">&quot;Mismatch between shared load iterator and output tile iterator.&quot;</span>);</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(OutputTileIterator::kElementsPerAccess, <span class="stringliteral">&quot;OutputTileIterator::kElementsPerAccess must not be zero.&quot;</span>);</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(!(OutputTileIterator::Fragment::kElements % OutputTileIterator::kElementsPerAccess), </div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    <span class="stringliteral">&quot;Divisibility&quot;</span>);</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a1dcbb6afb4bafd601e20e77fb241705a">SharedLoadIterator</a> shared_load_iterator_;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00159"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a3f87787b4dc2c705fd30c4ac98b080cf">  159</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a3f87787b4dc2c705fd30c4ac98b080cf">Epilogue</a>(</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;    <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html">Base::SharedStorage</a> &amp;shared_storage,    </div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;    <span class="keywordtype">int</span> thread_idx,                   </div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    <span class="keywordtype">int</span> warp_idx,                     </div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;    <span class="keywordtype">int</span> lane_idx                      </div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;  ):</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html">Base</a>(shared_storage, thread_idx, warp_idx, lane_idx),</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;    shared_load_iterator_(shared_storage.reference(), thread_idx) { }</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00170"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ac7819d17866af3356d4f7d4b8d4c0c2c">  170</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ac7819d17866af3356d4f7d4b8d4c0c2c">operator()</a>(</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a549eece4fc64291c8f0410dcb20d560a">OutputOp</a> <span class="keyword">const</span> &amp;output_op,                    </div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a977dbfb13c48512446e045b709368038">OutputTileIterator</a> destination_iterator,      </div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a391a932cd8341c5934cc48ec5fa4c0ab">AccumulatorTile</a> <span class="keyword">const</span> &amp;accumulators,          </div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a977dbfb13c48512446e045b709368038">OutputTileIterator</a> source_iterator) {         </div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;    <span class="keyword">typename</span> OutputTileIterator::Fragment source_fragment;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;    <span class="keywordflow">if</span> (!output_op.is_source_needed()) {</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;      source_iterator.clear_mask();</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;    }</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;    source_fragment.clear();</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;    <span class="comment">// Iterator over warp-level accumulator fragment</span></div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a0c3840f9e6462afeaa4cff567360912b">AccumulatorFragmentIterator</a> accum_fragment_iterator(accumulators);</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;    <span class="comment">//</span></div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;    <span class="comment">// Iterate over accumulator tile</span></div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;    <span class="comment">// </span></div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> iter = 0; iter &lt; OutputTileIterator::kIterations; ++iter) {</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;      <span class="comment">//</span></div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;      <span class="comment">// Load the source</span></div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;      <span class="comment">//</span></div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;      source_iterator.load(source_fragment);</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;      ++source_iterator;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;      <span class="comment">//</span></div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;      <span class="comment">// Convert and store fragment</span></div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;      <span class="comment">//</span></div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;      </div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;      __syncthreads();</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;      <span class="keyword">typename</span> AccumulatorFragmentIterator::Fragment accum_fragment;</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;      accum_fragment_iterator.load(accum_fragment);</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;      ++accum_fragment_iterator;</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;      this-&gt;<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#ac858903691c5e2b52c4a470e712911e3">warp_tile_iterator_</a>.store(accum_fragment);</div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;      __syncthreads();</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;      <span class="comment">//</span></div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;      <span class="comment">// Load fragments from shared memory</span></div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;      <span class="comment">//</span></div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;      <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#afb37ed9a0f3873600b9e743b2dcb805e">SharedLoadIterator::Fragment</a> aligned_accum_fragment[<a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ab08e1b54d71da091b702f28b5741b086">kPartitionsK</a>];</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;      shared_load_iterator_.load(aligned_accum_fragment[0]);</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;      <span class="comment">// If the number of k-slices is &gt; 1 - perform a reduction amongst the k-slices</span></div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;      <span class="keywordflow">if</span> (kPartitionsK &gt; 1)</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;      {</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;        <a class="code" href="structcutlass_1_1plus.html">plus &lt;typename SharedLoadIterator::Fragment&gt;</a> add_fragments;</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;        <span class="keyword">const</span> <span class="keywordtype">int</span> tile_row_offset = Base::SharedStorage::StorageShape::kRow / PartitionsK;</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;        <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;        <span class="keywordflow">for</span> ( <span class="keywordtype">int</span> i = 1; i &lt; <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ab08e1b54d71da091b702f28b5741b086">kPartitionsK</a>; ++i) {</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;          shared_load_iterator_.add_tile_offset({tile_row_offset , 0});</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;          shared_load_iterator_.load(aligned_accum_fragment[i]);</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;          aligned_accum_fragment[0] = add_fragments(aligned_accum_fragment[0], aligned_accum_fragment[i]);</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;        }</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;        shared_load_iterator_.add_tile_offset({-1 * (kPartitionsK-1) * tile_row_offset, 0});</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;      }</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;      <span class="comment">//</span></div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;      <span class="comment">// Compute the output result</span></div><div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;      <span class="comment">//</span></div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;     </div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;      <span class="keyword">typename</span> OutputTileIterator::Fragment output_fragment;</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;</div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;      apply_output_operator_(output_fragment, output_op, aligned_accum_fragment[0], source_fragment);</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;</div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;      <span class="comment">//</span></div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;      <span class="comment">// Store the final result</span></div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;      <span class="comment">//</span></div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;      destination_iterator.store(output_fragment);      </div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;      ++destination_iterator;</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;    }</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;  }</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;  <span class="keywordtype">void</span> apply_output_operator_(</div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;    <span class="keyword">typename</span> OutputTileIterator::Fragment &amp;output_fragment,</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a549eece4fc64291c8f0410dcb20d560a">OutputOp</a> <span class="keyword">const</span> &amp;output_op,                    </div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;    <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#afb37ed9a0f3873600b9e743b2dcb805e">SharedLoadIterator::Fragment</a> <span class="keyword">const</span> &amp;aligned_accum_fragment,</div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;    <span class="keyword">typename</span> OutputTileIterator::Fragment <span class="keyword">const</span> &amp;source_fragment) {</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;      </div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a53f99fb2f4a1abd0ad56ce7c6e1bdac2">OutputAccessType</a> *output_frag_ptr = </div><div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;      <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a53f99fb2f4a1abd0ad56ce7c6e1bdac2">OutputAccessType</a> *<span class="keyword">&gt;</span>(&amp;output_fragment);</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a0cfa64af365e51d50549528edea00692">AccumulatorAccessType</a> <span class="keyword">const</span> *compute_frag_ptr = </div><div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;      <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a0cfa64af365e51d50549528edea00692">AccumulatorAccessType</a> <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;aligned_accum_fragment);</div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a53f99fb2f4a1abd0ad56ce7c6e1bdac2">OutputAccessType</a> <span class="keyword">const</span> *source_frag_ptr = </div><div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;      <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a53f99fb2f4a1abd0ad56ce7c6e1bdac2">OutputAccessType</a> <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;source_fragment);</div><div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;    <span class="keywordtype">int</span> <span class="keyword">const</span> kOutputOpIterations = </div><div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;      OutputTileIterator::Fragment::kElements / OutputTileIterator::kElementsPerAccess;</div><div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;</div><div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; kOutputOpIterations; ++i) {</div><div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;</div><div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;      <span class="comment">// Call the output operator</span></div><div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;      output_frag_ptr[i] = output_op(compute_frag_ptr[i], source_frag_ptr[i]);</div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;    }</div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;  }</div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;};</div><div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;</div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;} <span class="comment">// namespace threadblock</span></div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;</div><div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html_a4062a36ab044fdea058504ed52ee60b8"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html#a4062a36ab044fdea058504ed52ee60b8">cutlass::layout::RowMajor::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:62</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a8a5f856300412017f5c12082af70b82f"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a8a5f856300412017f5c12082af70b82f">cutlass::epilogue::threadblock::Epilogue::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> epilogue.h:105</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a9b0cbcf46b87ddda5b2024d2facb07ce"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a9b0cbcf46b87ddda5b2024d2facb07ce">cutlass::epilogue::threadblock::Epilogue::WarpCount</a></div><div class="ttdeci">typename Base::WarpCount WarpCount</div><div class="ttdoc">Number of warps. </div><div class="ttdef"><b>Definition:</b> epilogue.h:137</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator_html_afb37ed9a0f3873600b9e743b2dcb805e"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1SharedLoadIterator.html#afb37ed9a0f3873600b9e743b2dcb805e">cutlass::epilogue::threadblock::SharedLoadIterator::Fragment</a></div><div class="ttdeci">Array&lt; Element, ThreadMap::Iterations::kColumn *ThreadMap::Iterations::kRow *ThreadMap::Iterations::kGroup *ThreadMap::Iterations::kCluster *ThreadMap::kElementsPerAccess &gt; Fragment</div><div class="ttdoc">Fragment object. </div><div class="ttdef"><b>Definition:</b> shared_load_iterator.h:91</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_ac858903691c5e2b52c4a470e712911e3"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#ac858903691c5e2b52c4a470e712911e3">cutlass::epilogue::threadblock::EpilogueBase::warp_tile_iterator_</a></div><div class="ttdeci">WarpTileIterator warp_tile_iterator_</div><div class="ttdoc">Stores a warp&amp;#39;s fragment of accumulators to SMEM. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:176</div></div>
<div class="ttc" id="pitch__linear__thread__map_8h_html"><div class="ttname"><a href="pitch__linear__thread__map_8h.html">pitch_linear_thread_map.h</a></div><div class="ttdoc">Templates implementing how threads are mapped to a given tile. </div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_1_1SharedStorage.html">cutlass::epilogue::threadblock::EpilogueBase::SharedStorage</a></div><div class="ttdoc">Shared storage allocation needed by the epilogue. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:97</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_ac7819d17866af3356d4f7d4b8d4c0c2c"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ac7819d17866af3356d4f7d4b8d4c0c2c">cutlass::epilogue::threadblock::Epilogue::operator()</a></div><div class="ttdeci">CUTLASS_DEVICE void operator()(OutputOp const &amp;output_op, OutputTileIterator destination_iterator, AccumulatorTile const &amp;accumulators, OutputTileIterator source_iterator)</div><div class="ttdoc">Streams the result to global memory. </div><div class="ttdef"><b>Definition:</b> epilogue.h:170</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a977dbfb13c48512446e045b709368038"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a977dbfb13c48512446e045b709368038">cutlass::epilogue::threadblock::Epilogue::OutputTileIterator</a></div><div class="ttdeci">OutputTileIterator_ OutputTileIterator</div><div class="ttdef"><b>Definition:</b> epilogue.h:96</div></div>
<div class="ttc" id="epilogue_2threadblock_2predicated__tile__iterator_8h_html"><div class="ttname"><a href="epilogue_2threadblock_2predicated__tile__iterator_8h.html">predicated_tile_iterator.h</a></div><div class="ttdoc">Epilogue for threadblock scoped GEMMs using Tensor Ops. </div></div>
<div class="ttc" id="include_2cutlass_2gemm_2gemm_8h_html"><div class="ttname"><a href="include_2cutlass_2gemm_2gemm_8h.html">gemm.h</a></div><div class="ttdoc">Defines common types used for all GEMM-like operators. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a3f87787b4dc2c705fd30c4ac98b080cf"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a3f87787b4dc2c705fd30c4ac98b080cf">cutlass::epilogue::threadblock::Epilogue::Epilogue</a></div><div class="ttdeci">CUTLASS_DEVICE Epilogue(typename Base::SharedStorage &amp;shared_storage, int thread_idx, int warp_idx, int lane_idx)</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> epilogue.h:159</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_ad9918897985656169962aaf48d16f273"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ad9918897985656169962aaf48d16f273">cutlass::epilogue::threadblock::Epilogue::Shape</a></div><div class="ttdeci">Shape_ Shape</div><div class="ttdef"><b>Definition:</b> epilogue.h:93</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a605570e320b4e342d3f7b23263d6ee37"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a605570e320b4e342d3f7b23263d6ee37">cutlass::epilogue::threadblock::Epilogue::TensorRef</a></div><div class="ttdeci">typename OutputTileIterator::TensorRef TensorRef</div><div class="ttdoc">Tensor reference to destination tensor. </div><div class="ttdef"><b>Definition:</b> epilogue.h:121</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_a4549c93a2946f616273e095a230e37bc"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a4549c93a2946f616273e095a230e37bc">cutlass::epilogue::threadblock::EpilogueBase::WarpCount</a></div><div class="ttdeci">gemm::GemmShape&lt; Shape::kM/WarpMmaOperator::Shape::kM, Shape::kN/WarpMmaOperator::Shape::kN, kPartitionsK &gt; WarpCount</div><div class="ttdoc">Number of warps. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:92</div></div>
<div class="ttc" id="structcutlass_1_1plus_html"><div class="ttname"><a href="structcutlass_1_1plus.html">cutlass::plus</a></div><div class="ttdef"><b>Definition:</b> functional.h:46</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="tensor_8h_html"><div class="ttname"><a href="tensor_8h.html">tensor.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes for common 4-D and 5-D tensor formats...</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_ab08e1b54d71da091b702f28b5741b086"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ab08e1b54d71da091b702f28b5741b086">cutlass::epilogue::threadblock::Epilogue::kPartitionsK</a></div><div class="ttdeci">static int const kPartitionsK</div><div class="ttdef"><b>Definition:</b> epilogue.h:95</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a549eece4fc64291c8f0410dcb20d560a"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a549eece4fc64291c8f0410dcb20d560a">cutlass::epilogue::threadblock::Epilogue::OutputOp</a></div><div class="ttdeci">OutputOp_ OutputOp</div><div class="ttdef"><b>Definition:</b> epilogue.h:100</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html"><div class="ttname"><a href="classcutlass_1_1TensorRef.html">cutlass::TensorRef</a></div><div class="ttdef"><b>Definition:</b> tensor_ref.h:146</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a36970da339d478df9807c01bd26fb87a"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a36970da339d478df9807c01bd26fb87a">cutlass::epilogue::threadblock::Epilogue::Padding</a></div><div class="ttdeci">Padding_ Padding</div><div class="ttdef"><b>Definition:</b> epilogue.h:101</div></div>
<div class="ttc" id="tensor__coord_8h_html"><div class="ttname"><a href="tensor__coord_8h.html">tensor_coord.h</a></div><div class="ttdoc">Defines a canonical coordinate for rank=4 tensors offering named indices. </div></div>
<div class="ttc" id="aligned__buffer_8h_html"><div class="ttname"><a href="aligned__buffer_8h.html">aligned_buffer.h</a></div><div class="ttdoc">AlignedBuffer is a container for trivially copyable elements suitable for use in unions and shared me...</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a0c3840f9e6462afeaa4cff567360912b"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a0c3840f9e6462afeaa4cff567360912b">cutlass::epilogue::threadblock::Epilogue::AccumulatorFragmentIterator</a></div><div class="ttdeci">AccumulatorFragmentIterator_ AccumulatorFragmentIterator</div><div class="ttdef"><b>Definition:</b> epilogue.h:97</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a92587dbbf9e08f1db3fab5443ae870e8"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a92587dbbf9e08f1db3fab5443ae870e8">cutlass::epilogue::threadblock::Epilogue::ConstTensorRef</a></div><div class="ttdeci">typename OutputTileIterator::ConstTensorRef ConstTensorRef</div><div class="ttdoc">Const tensor reference to source tensor. </div><div class="ttdef"><b>Definition:</b> epilogue.h:127</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a0cc6b8507106d9cd35ea1628adb3398c"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a0cc6b8507106d9cd35ea1628adb3398c">cutlass::epilogue::threadblock::Epilogue::WarpTileIterator</a></div><div class="ttdeci">WarpTileIterator_ WarpTileIterator</div><div class="ttdef"><b>Definition:</b> epilogue.h:98</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a1dcbb6afb4bafd601e20e77fb241705a"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a1dcbb6afb4bafd601e20e77fb241705a">cutlass::epilogue::threadblock::Epilogue::SharedLoadIterator</a></div><div class="ttdeci">SharedLoadIterator_ SharedLoadIterator</div><div class="ttdef"><b>Definition:</b> epilogue.h:99</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html">cutlass::epilogue::threadblock::Epilogue</a></div><div class="ttdoc">Epilogue operator without splitk. </div><div class="ttdef"><b>Definition:</b> epilogue.h:74</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a3c4101ad87f1e04472c332c012d8b3df"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a3c4101ad87f1e04472c332c012d8b3df">cutlass::epilogue::threadblock::Epilogue::ElementAccumulator</a></div><div class="ttdeci">typename WarpTileIterator::Element ElementAccumulator</div><div class="ttdoc">Accumulator element. </div><div class="ttdef"><b>Definition:</b> epilogue.h:111</div></div>
<div class="ttc" id="vector_8h_html"><div class="ttname"><a href="vector_8h.html">vector.h</a></div><div class="ttdoc">Defines layout functions used for rank=1 vectors. </div></div>
<div class="ttc" id="regular__tile__iterator_8h_html"><div class="ttname"><a href="regular__tile__iterator_8h.html">regular_tile_iterator.h</a></div><div class="ttdoc">Templates implementing storing of tiles from pitch-linear rank=2 tensors. </div></div>
<div class="ttc" id="epilogue__base_8h_html"><div class="ttname"><a href="epilogue__base_8h.html">epilogue_base.h</a></div><div class="ttdoc">Epilogue for threadblock scoped GEMMs using Tensor Ops. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html">cutlass::epilogue::threadblock::EpilogueBase</a></div><div class="ttdoc">Base class for epilogues defining warp-level. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:67</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a08c18f01a998cfb1eea765abbe9c902e"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a08c18f01a998cfb1eea765abbe9c902e">cutlass::epilogue::threadblock::Epilogue::WarpMmaOperator</a></div><div class="ttdeci">WarpMmaOperator_ WarpMmaOperator</div><div class="ttdef"><b>Definition:</b> epilogue.h:94</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a391a932cd8341c5934cc48ec5fa4c0ab"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a391a932cd8341c5934cc48ec5fa4c0ab">cutlass::epilogue::threadblock::Epilogue::AccumulatorTile</a></div><div class="ttdeci">typename Base::AccumulatorTile AccumulatorTile</div><div class="ttdoc">The complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> epilogue.h:108</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a53f99fb2f4a1abd0ad56ce7c6e1bdac2"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a53f99fb2f4a1abd0ad56ce7c6e1bdac2">cutlass::epilogue::threadblock::Epilogue::OutputAccessType</a></div><div class="ttdeci">Array&lt; typename OutputTileIterator::Element, OutputTileIterator::kElementsPerAccess &gt; OutputAccessType</div><div class="ttdoc">Array type used to output. </div><div class="ttdef"><b>Definition:</b> epilogue.h:131</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_af8ecba80487465f25daac670e661627e"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#af8ecba80487465f25daac670e661627e">cutlass::epilogue::threadblock::Epilogue::kElementsPerAccess</a></div><div class="ttdeci">static int const kElementsPerAccess</div><div class="ttdoc">Output access size. </div><div class="ttdef"><b>Definition:</b> epilogue.h:118</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase_html_a1707fb90363342996902b96ccd3bb176"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1EpilogueBase.html#a1707fb90363342996902b96ccd3bb176">cutlass::epilogue::threadblock::EpilogueBase::AccumulatorTile</a></div><div class="ttdeci">typename AccumulatorFragmentIterator::AccumulatorTile AccumulatorTile</div><div class="ttdoc">The complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> epilogue_base.h:81</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_ab0570eddc6943f2607762ec49a1ec174"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#ab0570eddc6943f2607762ec49a1ec174">cutlass::epilogue::threadblock::Epilogue::ElementOutput</a></div><div class="ttdeci">typename OutputTileIterator::Element ElementOutput</div><div class="ttdoc">Output element. </div><div class="ttdef"><b>Definition:</b> epilogue.h:115</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a9087b022bc5baed5bdf5e828cff31d48"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a9087b022bc5baed5bdf5e828cff31d48">cutlass::epilogue::threadblock::Epilogue::SyncTensorRef</a></div><div class="ttdeci">typename cutlass::TensorRef&lt; int, cutlass::layout::PackedVectorLayout &gt; SyncTensorRef</div><div class="ttdoc">Tensor reference to sync tensor. </div><div class="ttdef"><b>Definition:</b> epilogue.h:124</div></div>
<div class="ttc" id="functional_8h_html"><div class="ttname"><a href="functional_8h.html">functional.h</a></div><div class="ttdoc">Define basic numeric operators with specializations for Array&lt;T, N&gt;. SIMD-ize where possible...</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue_html_a0cfa64af365e51d50549528edea00692"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1threadblock_1_1Epilogue.html#a0cfa64af365e51d50549528edea00692">cutlass::epilogue::threadblock::Epilogue::AccumulatorAccessType</a></div><div class="ttdeci">Array&lt; typename WarpTileIterator::Element, OutputTileIterator::kElementsPerAccess &gt; AccumulatorAccessType</div><div class="ttdoc">Array type used by output functor. </div><div class="ttdef"><b>Definition:</b> epilogue.h:134</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
