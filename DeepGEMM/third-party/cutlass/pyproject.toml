[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[project]
name = "nvidia-cutlass"
version = "*******"
description = "CUTLASS"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "BSD-3-Clause"}
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: BSD License",
    "Operating System :: OS Independent",
]
dependencies = [
  "cuda-python>=11.8.0",
  "networkx",
  "numpy",
  "pydot",
  "scipy",
  "treelib"
]

[project.urls]
"Homepage" = "https://github.com/nvidia/cutlass"
"Bug Tracker" = "https://github.com/nvidia/cutlass/issues"
